<%@ Page Title="Messagerie Moderne" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie-moderne.aspx.cs" Inherits="LinCom.messagerie_moderne" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Hidden Fields -->
    <asp:HiddenField ID="hdnConversationId" runat="server" />
    <asp:HiddenField ID="hdnIsGroup" runat="server" />
    <asp:HiddenField ID="hdnCurrentUserId" runat="server" />
    <asp:HiddenField ID="hdnSelectedContactId" runat="server" />

    <!-- Modern Chat Interface -->
    <div class="modern-chat-container">
        
        <!-- Sidebar - Liste des Conversations -->
        <div class="chat-sidebar">
            <!-- Header Sidebar -->
            <div class="sidebar-header">
                <div class="user-profile">
                    <div class="user-avatar">
                        <img src="assets/img/default-avatar.png" alt="Mon profil" id="currentUserAvatar">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="user-info">
                        <h4 id="currentUserName">Mon Profil</h4>
                        <span class="user-status">En ligne</span>
                    </div>
                </div>
                <div class="sidebar-actions">
                    <button class="btn-icon" title="Nouvelle conversation" onclick="ouvrirNouvelleConversation()">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" title="Options" onclick="ouvrirOptions()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- Barre de Recherche -->
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="txtRechercheConversation" placeholder="Rechercher une conversation..." 
                           onkeyup="rechercherConversations(this.value)" autocomplete="off">
                    <button class="clear-search" onclick="viderRecherche()" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Liste des Conversations -->
            <div class="conversations-list" id="conversationsList">
                <asp:ListView ID="lvConversations" runat="server" OnItemCommand="lvConversations_ItemCommand">
                    <EmptyDataTemplate>
                        <div class="empty-conversations">
                            <i class="fas fa-comments"></i>
                            <h3>Aucune conversation</h3>
                            <p>Commencez une nouvelle conversation en cliquant sur le bouton +</p>
                        </div>
                    </EmptyDataTemplate>
                    <ItemTemplate>
                        <div class="conversation-item <%# Container.DataItemIndex == 0 ? "active" : "" %>" 
                             data-conversation-id='<%# Eval("ConversationId") %>'
                             onclick="selectionnerConversation(this, <%# Eval("ConversationId") %>)">
                            <div class="conversation-avatar">
                                <img src='<%# Eval("PhotoProfil", "assets/img/default-avatar.png") %>' alt="Avatar">
                                <div class="status-indicator <%# Eval("Statut", "offline") %>"></div>
                            </div>
                            <div class="conversation-content">
                                <div class="conversation-header">
                                    <h4 class="conversation-name"><%# Eval("NomConversation") %></h4>
                                    <span class="conversation-time"><%# Eval("DernierMessageDate", "{0:HH:mm}") %></span>
                                </div>
                                <div class="conversation-preview">
                                    <p class="last-message"><%# Eval("DernierMessage") %></p>
                                    <div class="conversation-badges">
                                        <span class="unread-count <%# Convert.ToInt32(Eval("MessagesNonLus")) > 0 ? "" : "hidden" %>">
                                            <%# Eval("MessagesNonLus") %>
                                        </span>
                                        <i class="fas fa-check-double message-status <%# Eval("StatutMessage") %>"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:ListView>
            </div>
        </div>

        <!-- Zone de Chat Principale -->
        <div class="chat-main">
            <!-- Header du Chat -->
            <div class="chat-header">
                <div class="chat-contact-info">
                    <div class="contact-avatar">
                        <img src="assets/img/default-avatar.png" alt="Contact" id="chatContactAvatar">
                        <div class="status-indicator online" id="chatContactStatus"></div>
                    </div>
                    <div class="contact-details">
                        <h3 id="chatContactName">Sélectionnez une conversation</h3>
                        <span class="contact-status" id="chatContactStatusText">Hors ligne</span>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="btn-icon" title="Rechercher dans la conversation">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn-icon" title="Appel vocal">
                        <i class="fas fa-phone"></i>
                    </button>
                    <button class="btn-icon" title="Appel vidéo">
                        <i class="fas fa-video"></i>
                    </button>
                    <button class="btn-icon" title="Informations">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>

            <!-- Zone des Messages -->
            <div class="chat-messages" id="chatMessages">
                <div class="messages-container">
                    <asp:ListView ID="lvMessages" runat="server">
                        <EmptyDataTemplate>
                            <div class="empty-chat">
                                <i class="fas fa-comments"></i>
                                <h3>Commencez la conversation</h3>
                                <p>Envoyez votre premier message pour démarrer la discussion</p>
                            </div>
                        </EmptyDataTemplate>
                        <ItemTemplate>
                            <div class="message <%# Convert.ToInt64(Eval("SenderId")) == GetCurrentUserId() ? "sent" : "received" %>">
                                <div class="message-avatar">
                                    <img src='<%# Eval("PhotoProfil", "assets/img/default-avatar.png") %>' alt="Avatar">
                                </div>
                                <div class="message-content">
                                    <div class="message-bubble">
                                        <div class="message-text"><%# Eval("Contenu") %></div>
                                        <%# !string.IsNullOrEmpty(Eval("AttachmentUrl")?.ToString()) ? 
                                            "<div class='message-attachment'><a href='" + Eval("AttachmentUrl") + "' target='_blank'><i class='fas fa-paperclip'></i> Pièce jointe</a></div>" : "" %>
                                    </div>
                                    <div class="message-info">
                                        <span class="message-time"><%# Eval("DateEnvoi", "{0:HH:mm}") %></span>
                                        <%# Convert.ToInt64(Eval("SenderId")) == GetCurrentUserId() ? 
                                            "<i class='fas fa-check-double message-status read'></i>" : "" %>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
                
                <!-- Indicateur de frappe -->
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="typing-avatar">
                        <img src="assets/img/default-avatar.png" alt="Avatar">
                    </div>
                    <div class="typing-bubble">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Zone de Saisie -->
            <div class="chat-input-container">
                <div class="input-wrapper">
                    <button class="btn-attachment" title="Joindre un fichier">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <div class="text-input-container">
                        <textarea id="txtMessage" runat="server" placeholder="Tapez votre message..." 
                                  onkeydown="gererToucheEntree(event)" oninput="gererSaisie()" rows="1"></textarea>
                        <button class="btn-emoji" title="Émojis" onclick="ouvrirEmojis()">
                            <i class="fas fa-smile"></i>
                        </button>
                    </div>
                    <button class="btn-send" id="btnEnvoyer" runat="server" onserverclick="btnEnvoyer_Click" title="Envoyer">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-actions">
                    <span class="char-counter" id="charCounter">0/5000</span>
                </div>
            </div>
        </div>

        <!-- Panel Latéral Droit (Informations) -->
        <div class="chat-info-panel" id="chatInfoPanel" style="display: none;">
            <div class="info-header">
                <h3>Informations</h3>
                <button class="btn-close" onclick="fermerInfoPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="info-content">
                <!-- Contenu des informations -->
            </div>
        </div>
    </div>

    <!-- Modal Nouvelle Conversation -->
    <div class="modal-overlay" id="modalNouvelleConversation" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Nouvelle Conversation</h3>
                <button class="btn-close" onclick="fermerModal('modalNouvelleConversation')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="search-contacts">
                    <input type="text" placeholder="Rechercher un contact..." onkeyup="rechercherContacts(this.value)">
                </div>
                <div class="contacts-list">
                    <asp:ListView ID="lvContacts" runat="server" OnItemCommand="lvContacts_ItemCommand">
                        <ItemTemplate>
                            <div class="contact-item" onclick="demarrerConversation(<%# Eval("MembreId") %>)">
                                <div class="contact-avatar">
                                    <img src='<%# Eval("PhotoProfil", "assets/img/default-avatar.png") %>' alt="Avatar">
                                </div>
                                <div class="contact-info">
                                    <h4><%# Eval("NomComplet") %></h4>
                                    <p><%# Eval("Email") %></p>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
