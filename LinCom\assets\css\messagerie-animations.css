/* ===== ANIMATIONS ET NOTIFICATIONS - MESSAGERIE MODERNE ===== */

/* ===== NOTIFICATIONS ===== */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
}

.notification {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    padding: 16px 20px;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 12px;
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    animation: slideInNotification 0.3s ease-out forwards;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
}

.notification.success::before {
    background: var(--success-color);
}

.notification.error::before {
    background: var(--error-color);
}

.notification.warning::before {
    background: var(--warning-color);
}

.notification.info::before {
    background: var(--info-color);
}

.notification-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error .notification-icon {
    color: var(--error-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.info .notification-icon {
    color: var(--info-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: var(--font-size-base);
    margin: 0 0 4px 0;
    color: var(--text-primary);
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Animations des notifications */
@keyframes slideInNotification {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutNotification {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.removing {
    animation: slideOutNotification 0.3s ease-in forwards;
}

/* ===== LOADING SPINNER ===== */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--text-muted);
}

.loading-spinner {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: var(--font-size-sm);
}

.loading-spinner i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* ===== EMOJI POPUP ===== */
.emoji-popup {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    padding: 12px;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    max-width: 240px;
    animation: popupSlideIn 0.2s ease-out;
}

.emoji-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.emoji-btn:hover {
    background: var(--bg-secondary);
    transform: scale(1.1);
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== FILE PREVIEW ===== */
.file-preview {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    margin-bottom: 12px;
    animation: slideInFile 0.3s ease-out;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-info i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.btn-remove-file {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.btn-remove-file:hover {
    background: var(--error-color);
    color: var(--text-white);
}

@keyframes slideInFile {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== HOVER EFFECTS ===== */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.hover-scale {
    transition: var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: var(--transition);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
}

/* ===== PULSE ANIMATION ===== */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 212, 170, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 212, 170, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 212, 170, 0);
    }
}

/* ===== SHAKE ANIMATION ===== */
.shake {
    animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

/* ===== FADE ANIMATIONS ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* ===== SLIDE ANIMATIONS ===== */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== ZOOM ANIMATIONS ===== */
.zoom-in {
    animation: zoomIn 0.3s ease-out;
}

.zoom-out {
    animation: zoomOut 0.3s ease-in;
}

@keyframes zoomIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes zoomOut {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(0.8);
        opacity: 0;
    }
}

/* ===== ROTATE ANIMATIONS ===== */
.rotate {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* ===== BOUNCE ANIMATIONS ===== */
.bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}
