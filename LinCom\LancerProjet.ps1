
param(
    [switch]$VerifierIntegration,
    [switch]$OuvrirVS,
    [switch]$CompilerSeulement,
    [switch]$AfficherAide
)

# Couleurs pour l'affichage
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"
$White = "White"

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor $Cyan
    Write-Host $Title -ForegroundColor $Green
    Write-Host "=" * 60 -ForegroundColor $Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "🔄 $Message" -ForegroundColor $Yellow
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️ $Message" -ForegroundColor $Cyan
}

function Show-Help {
    Write-Header "🚀 Script de Lancement LinCom - Aide"
    
    Write-Host "UTILISATION:" -ForegroundColor $White
    Write-Host "  .\LancerProjet.ps1 [OPTIONS]" -ForegroundColor $White
    Write-Host ""
    
    Write-Host "OPTIONS:" -ForegroundColor $White
    Write-Host "  -VerifierIntegration    Vérifier l'intégration des améliorations" -ForegroundColor $White
    Write-Host "  -OuvrirVS              Ouvrir le projet dans Visual Studio 2022" -ForegroundColor $White
    Write-Host "  -CompilerSeulement     Compiler le projet sans l'ouvrir" -ForegroundColor $White
    Write-Host "  -AfficherAide          Afficher cette aide" -ForegroundColor $White
    Write-Host ""
    
    Write-Host "EXEMPLES:" -ForegroundColor $White
    Write-Host "  .\LancerProjet.ps1                    # Lancement complet" -ForegroundColor $White
    Write-Host "  .\LancerProjet.ps1 -VerifierIntegration  # Vérification seulement" -ForegroundColor $White
    Write-Host "  .\LancerProjet.ps1 -OuvrirVS            # Ouvrir VS2022 seulement" -ForegroundColor $White
    Write-Host ""
    
    Write-Host "FICHIERS IMPORTANTS:" -ForegroundColor $White
    Write-Host "  📁 LinCom.sln                    - Solution Visual Studio" -ForegroundColor $White
    Write-Host "  🧪 test-messagerie.aspx          - Page de test des améliorations" -ForegroundColor $White
    Write-Host "  📚 README_VS2022.md              - Guide de démarrage rapide" -ForegroundColor $White
    Write-Host "  📖 AMELIORATIONS_MESSAGERIE.md   - Documentation complète" -ForegroundColor $White
    Write-Host ""
}

function Test-Prerequisites {
    Write-Step "Vérification des prérequis..."
    
    # Vérifier que nous sommes dans le bon dossier
    if (-not (Test-Path "LinCom.sln")) {
        Write-Error "Fichier LinCom.sln non trouvé. Assurez-vous d'être dans le bon dossier."
        return $false
    }
    
    # Vérifier Visual Studio 2022
    $vsPath = Get-Command "devenv.exe" -ErrorAction SilentlyContinue
    if (-not $vsPath) {
        # Essayer les chemins standards
        $standardPaths = @(
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe"
        )
        
        $found = $false
        foreach ($path in $standardPaths) {
            if (Test-Path $path) {
                $global:VSPath = $path
                $found = $true
                break
            }
        }
        
        if (-not $found) {
            Write-Error "Visual Studio 2022 non trouvé. Veuillez l'installer ou ajouter devenv.exe au PATH."
            return $false
        }
    } else {
        $global:VSPath = $vsPath.Source
    }
    
    Write-Success "Prérequis vérifiés"
    return $true
}

function Verify-Integration {
    Write-Step "Vérification de l'intégration des améliorations..."
    
    if (Test-Path "VerifierIntegration.ps1") {
        & ".\VerifierIntegration.ps1"
    } else {
        Write-Error "Script de vérification non trouvé"
        return $false
    }
    
    return $true
}

function Open-VisualStudio {
    Write-Step "Ouverture de Visual Studio 2022..."
    
    try {
        Start-Process -FilePath $global:VSPath -ArgumentList "LinCom.sln" -NoNewWindow
        Write-Success "Visual Studio 2022 lancé avec le projet LinCom"
        
        # Attendre un peu puis afficher les instructions
        Start-Sleep -Seconds 3
        Write-Info "Instructions pour Visual Studio 2022:"
        Write-Host "  1. Attendez que le projet se charge complètement" -ForegroundColor $White
        Write-Host "  2. Appuyez sur F5 ou Ctrl+F5 pour lancer l'application" -ForegroundColor $White
        Write-Host "  3. Testez les améliorations sur /test-messagerie.aspx" -ForegroundColor $White
        Write-Host "  4. Utilisez la messagerie améliorée sur /messagerie.aspx" -ForegroundColor $White
        
        return $true
    } catch {
        Write-Error "Erreur lors du lancement de Visual Studio: $($_.Exception.Message)"
        return $false
    }
}

function Compile-Project {
    Write-Step "Compilation du projet..."
    
    try {
        # Utiliser MSBuild pour compiler
        $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
        if (-not (Test-Path $msbuildPath)) {
            $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
        }
        if (-not (Test-Path $msbuildPath)) {
            $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
        }
        
        if (Test-Path $msbuildPath) {
            & $msbuildPath "LinCom.sln" /p:Configuration=Debug /p:Platform="Any CPU"
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Compilation réussie"
                return $true
            } else {
                Write-Error "Erreur de compilation"
                return $false
            }
        } else {
            Write-Error "MSBuild non trouvé"
            return $false
        }
    } catch {
        Write-Error "Erreur lors de la compilation: $($_.Exception.Message)"
        return $false
    }
}

function Show-ProjectInfo {
    Write-Header "📊 Informations du Projet LinCom"
    
    Write-Host "🎯 NOUVELLES FONCTIONNALITÉS:" -ForegroundColor $White
    Write-Host "  ✅ Sécurité renforcée (validation, sanitisation, rate limiting)" -ForegroundColor $Green
    Write-Host "  ✅ Performance optimisée (cache, pagination)" -ForegroundColor $Green
    Write-Host "  ✅ Interface moderne (recherche temps réel, validation client)" -ForegroundColor $Green
    Write-Host ""
    
    Write-Host "🧪 TESTS DISPONIBLES:" -ForegroundColor $White
    Write-Host "  📄 /test-messagerie.aspx - Tests automatisés des améliorations" -ForegroundColor $Cyan
    Write-Host "  💬 /messagerie.aspx - Messagerie avec nouvelles fonctionnalités" -ForegroundColor $Cyan
    Write-Host ""
    
    Write-Host "📚 DOCUMENTATION:" -ForegroundColor $White
    Write-Host "  📖 README_VS2022.md - Guide de démarrage rapide" -ForegroundColor $Cyan
    Write-Host "  📋 AMELIORATIONS_MESSAGERIE.md - Documentation technique" -ForegroundColor $Cyan
    Write-Host "  🔧 GUIDE_VISUAL_STUDIO_2022.md - Guide détaillé VS2022" -ForegroundColor $Cyan
    Write-Host ""
}

# Script principal
Write-Header "🚀 Lanceur de Projet LinCom - Visual Studio 2022"

if ($AfficherAide) {
    Show-Help
    exit 0
}

# Vérifier les prérequis
if (-not (Test-Prerequisites)) {
    exit 1
}

# Afficher les informations du projet
Show-ProjectInfo

# Exécuter les actions demandées
$success = $true

if ($VerifierIntegration -or (-not $OuvrirVS -and -not $CompilerSeulement)) {
    if (-not (Verify-Integration)) {
        $success = $false
    }
}

if ($CompilerSeulement -or (-not $OuvrirVS -and -not $VerifierIntegration)) {
    if (-not (Compile-Project)) {
        $success = $false
    }
}

if ($OuvrirVS -or (-not $VerifierIntegration -and -not $CompilerSeulement)) {
    if (-not (Open-VisualStudio)) {
        $success = $false
    }
}

# Résumé final
Write-Header "📋 Résumé"

if ($success) {
    Write-Success "Toutes les opérations ont été exécutées avec succès !"
    Write-Host ""
    Write-Host "🎉 PROCHAINES ÉTAPES:" -ForegroundColor $Green
    Write-Host "  1. Dans Visual Studio 2022, appuyez sur F5 pour lancer" -ForegroundColor $White
    Write-Host "  2. Naviguez vers /test-messagerie.aspx pour tester" -ForegroundColor $White
    Write-Host "  3. Utilisez /messagerie.aspx pour la messagerie améliorée" -ForegroundColor $White
    Write-Host "  4. Consultez la documentation pour plus de détails" -ForegroundColor $White
} else {
    Write-Error "Certaines opérations ont échoué. Consultez les messages ci-dessus."
    Write-Host ""
    Write-Host "🔧 ACTIONS RECOMMANDÉES:" -ForegroundColor $Yellow
    Write-Host "  1. Vérifiez que Visual Studio 2022 est installé" -ForegroundColor $White
    Write-Host "  2. Assurez-vous d'être dans le bon dossier" -ForegroundColor $White
    Write-Host "  3. Relancez le script avec -AfficherAide pour plus d'infos" -ForegroundColor $White
}

Write-Host ""
Write-Host "📞 Support: Consultez README_VS2022.md ou GUIDE_VISUAL_STUDIO_2022.md" -ForegroundColor $Cyan
Write-Host "🕒 $(Get-Date)" -ForegroundColor $Cyan
