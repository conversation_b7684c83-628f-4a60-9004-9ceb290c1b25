<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bouton Envoi - Messagerie LinCom</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="assets/css/messagerie-amelioree.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #008374, #006b5e);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #008374;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .demo-chat {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
        }
        
        .demo-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .demo-input-area {
            padding: 15px;
            border-top: 1px solid #ddd;
            background: white;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-bug"></i> Test Bouton Envoi - Messagerie LinCom</h1>
            <p>Test interactif pour vérifier le fonctionnement du bouton d'envoi</p>
        </div>
        
        <div class="test-content">
            <!-- Section 1: Test du Bouton -->
            <div class="test-section">
                <h3><i class="fas fa-play-circle"></i> Test Interactif</h3>
                <p>Tapez du texte dans la zone ci-dessous pour voir le bouton s'activer :</p>
                
                <div class="demo-chat">
                    <div class="demo-messages" id="demoMessages">
                        <div class="message received">
                            <div class="message-content">
                                <div class="message-bubble">
                                    <div class="message-text">Bonjour ! Tapez votre message ci-dessous pour tester.</div>
                                </div>
                                <div class="message-info">
                                    <span class="message-time">14:30</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="demo-input-area">
                        <div class="message-input-container">
                            <div class="textarea-wrapper">
                                <textarea id="testMessage" 
                                         placeholder="Écrivez votre message..." 
                                         maxlength="5000"
                                         rows="1"></textarea>
                            </div>
                            
                            <button type="button" 
                                   id="testSendButton" 
                                   class="btn-send"
                                   disabled
                                   title="Envoyer le message">
                                <i class="fas fa-paper-plane"></i>
                                <span class="btn-text">Envoyer</span>
                            </button>
                        </div>
                        
                        <div class="message-footer-info">
                            <div class="left-info">
                                <span id="testCharCount" class="char-counter">0/5000</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Section 2: État du Bouton -->
            <div class="test-section">
                <h3><i class="fas fa-info-circle"></i> État du Bouton</h3>
                <div id="buttonStatus" class="status info">
                    <strong>État :</strong> <span id="statusText">Désactivé (aucun texte)</span>
                </div>
                <div id="buttonProperties">
                    <p><strong>Disabled :</strong> <span id="disabledStatus">true</span></p>
                    <p><strong>Classes CSS :</strong> <span id="cssClasses">btn-send</span></p>
                    <p><strong>Style Background :</strong> <span id="backgroundStyle">#ccc</span></p>
                </div>
            </div>
            
            <!-- Section 3: Log des Événements -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> Log des Événements</h3>
                <div id="eventLog" class="log">Prêt pour les tests...\n</div>
                <button onclick="clearLog()" class="btn-send ready" style="margin-top: 10px;">
                    <i class="fas fa-trash"></i> Effacer le Log
                </button>
            </div>
            
            <!-- Section 4: Tests Automatiques -->
            <div class="test-section">
                <h3><i class="fas fa-robot"></i> Tests Automatiques</h3>
                <button onclick="runAutomaticTests()" class="btn-send ready">
                    <i class="fas fa-play"></i> Lancer les Tests
                </button>
                <div id="testResults"></div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let messageInput, sendButton, charCounter, eventLog;
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            messageInput = document.getElementById('testMessage');
            sendButton = document.getElementById('testSendButton');
            charCounter = document.getElementById('testCharCount');
            eventLog = document.getElementById('eventLog');
            
            setupEventListeners();
            logEvent('✅ Initialisation terminée');
        });
        
        function setupEventListeners() {
            if (messageInput) {
                messageInput.addEventListener('input', function() {
                    updateButtonState();
                    logEvent(`✍️ Texte saisi: "${this.value}" (${this.value.length} caractères)`);
                });
                
                messageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (!sendButton.disabled) {
                            sendMessage();
                        } else {
                            logEvent('⚠️ Tentative d\'envoi avec bouton désactivé');
                        }
                    }
                });
            }
            
            if (sendButton) {
                sendButton.addEventListener('click', function() {
                    sendMessage();
                });
            }
        }
        
        function updateButtonState() {
            if (!messageInput || !sendButton) return;
            
            const hasText = messageInput.value.trim().length > 0;
            const charCount = messageInput.value.length;
            
            // Mettre à jour le compteur
            if (charCounter) {
                charCounter.textContent = charCount + '/5000';
                charCounter.className = 'char-counter';
                if (charCount > 4500) {
                    charCounter.classList.add('danger');
                } else if (charCount > 4000) {
                    charCounter.classList.add('warning');
                }
            }
            
            // État du bouton
            const wasDisabled = sendButton.disabled;
            sendButton.disabled = !hasText || charCount > 5000;
            
            // Classes CSS
            sendButton.classList.remove('disabled', 'ready');
            if (hasText && charCount <= 5000) {
                sendButton.classList.add('ready');
            } else {
                sendButton.classList.add('disabled');
            }
            
            // Mise à jour de l'affichage de l'état
            updateStatusDisplay();
            
            if (wasDisabled !== sendButton.disabled) {
                logEvent(`🔄 État du bouton changé: ${sendButton.disabled ? 'DÉSACTIVÉ' : 'ACTIVÉ'}`);
            }
        }
        
        function updateStatusDisplay() {
            const statusText = document.getElementById('statusText');
            const disabledStatus = document.getElementById('disabledStatus');
            const cssClasses = document.getElementById('cssClasses');
            const backgroundStyle = document.getElementById('backgroundStyle');
            const buttonStatus = document.getElementById('buttonStatus');
            
            if (sendButton.disabled) {
                statusText.textContent = 'Désactivé';
                buttonStatus.className = 'status error';
            } else {
                statusText.textContent = 'Activé - Prêt à envoyer';
                buttonStatus.className = 'status success';
            }
            
            disabledStatus.textContent = sendButton.disabled;
            cssClasses.textContent = sendButton.className;
            backgroundStyle.textContent = getComputedStyle(sendButton).backgroundColor;
        }
        
        function sendMessage() {
            const messageText = messageInput.value.trim();
            if (!messageText) return;
            
            logEvent(`🚀 ENVOI DU MESSAGE: "${messageText}"`);
            
            // Changer l'état du bouton
            sendButton.disabled = true;
            sendButton.classList.remove('ready');
            sendButton.classList.add('sending');
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span class="btn-text">Envoi...</span>';
            
            // Afficher le message
            displayMessage(messageText);
            
            // Vider le champ
            messageInput.value = '';
            updateButtonState();
            
            // Simuler l'envoi
            setTimeout(function() {
                sendButton.classList.remove('sending');
                sendButton.classList.add('sent');
                sendButton.innerHTML = '<i class="fas fa-check"></i><span class="btn-text">Envoyé</span>';
                logEvent('✅ Message envoyé avec succès');
                
                setTimeout(function() {
                    sendButton.classList.remove('sent');
                    sendButton.innerHTML = '<i class="fas fa-paper-plane"></i><span class="btn-text">Envoyer</span>';
                    updateButtonState();
                    logEvent('🔄 Bouton remis à l\'état initial');
                }, 2000);
            }, 1000);
        }
        
        function displayMessage(text) {
            const messagesContainer = document.getElementById('demoMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message sent new';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="message-text">${escapeHtml(text)}</div>
                    </div>
                    <div class="message-info">
                        <span class="message-time">${new Date().toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</span>
                        <i class="fas fa-clock message-status-icon sending" title="Envoi en cours"></i>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function logEvent(message) {
            const timestamp = new Date().toLocaleTimeString();
            eventLog.textContent += `[${timestamp}] ${message}\n`;
            eventLog.scrollTop = eventLog.scrollHeight;
        }
        
        function clearLog() {
            eventLog.textContent = 'Log effacé...\n';
        }
        
        function runAutomaticTests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h4>Tests en cours...</h4>';
            
            const tests = [
                { name: 'Bouton désactivé par défaut', test: () => sendButton.disabled },
                { name: 'Compteur à 0 par défaut', test: () => charCounter.textContent === '0/5000' },
                { name: 'Classe disabled présente', test: () => sendButton.classList.contains('disabled') },
            ];
            
            let html = '<h4>Résultats des Tests :</h4>';
            tests.forEach(test => {
                const result = test.test();
                html += `<div class="status ${result ? 'success' : 'error'}">
                    ${result ? '✅' : '❌'} ${test.name}
                </div>`;
            });
            
            // Test avec texte
            messageInput.value = 'Test automatique';
            updateButtonState();
            
            const textTests = [
                { name: 'Bouton activé avec texte', test: () => !sendButton.disabled },
                { name: 'Classe ready présente', test: () => sendButton.classList.contains('ready') },
                { name: 'Compteur mis à jour', test: () => charCounter.textContent === '15/5000' },
            ];
            
            textTests.forEach(test => {
                const result = test.test();
                html += `<div class="status ${result ? 'success' : 'error'}">
                    ${result ? '✅' : '❌'} ${test.name}
                </div>`;
            });
            
            messageInput.value = '';
            updateButtonState();
            
            results.innerHTML = html;
        }
    </script>
</body>
</html>
