using Antlr.Runtime.Tree;
using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ConversationImp : IConversation
    {
        int msg;
        private Conversation conversation = new Conversation();

        public void AfficherDetails(long conversationId, Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationId);
                if (c != null)
                {
                    conversationClass.ConversationId = c.ConversationId;
                    conversationClass.Sujet = c.Sujet;
                    conversationClass.IsGroup = c.IsGroup;
                    conversationClass.CreatedAt = c.CreatedAt;

    }
            }
        }

        public int AjouterParticipant(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                if (!con.ParticipantConversations.Any(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId))
                {
                    var participant = new ParticipantConversation
                    {
                        ConversationId = conversationId,
                        MembreId = membreId,
                        JoinedAt =DateTime.Now,

    };

                    try
                    {
                        con.ParticipantConversations.Add(participant);
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public long VerifierConversationId(long membre1Id, long membre2Id)
        {
            using (var con = new Connection())
            {
                //    // Recherche la conversation privée avec exactement ces deux membres
                //    var conversationId = (from pc in con.ParticipantConversations
                //                          where pc.MembreId == membre1Id || pc.MembreId == membre2Id
                //                          group pc by pc.ConversationId into grp
                //                          where grp.Count() == 2 // exact 2 participants
                //                          join conv in con.Conversations on grp.Key equals conv.ConversationId
                //                          where conv.IsGroup == 0 // conversation privée
                //                          select conv.ConversationId).FirstOrDefault();

                //    // Si aucune conversation, FirstOrDefault retourne 0 (long default)
                //    if (conversationId > 0)
                //    {
                //        // Conversation trouvée, on retourne son ID
                //        return conversationId;
                //    }
                //    else
                //    {
                //        // Pas de conversation trouvée entre ces deux membres
                //        return 0;
                //    }
                //}
                var conversationId = (
               from conv in con.Conversations
               where conv.IsGroup == 0
               join pc in con.ParticipantConversations on conv.ConversationId equals pc.ConversationId
               group pc by pc.ConversationId into grp
               where grp.Count() == 2 &&
                     grp.Any(p => p.MembreId == membre1Id) &&
                     grp.Any(p => p.MembreId == membre2Id)
               select grp.Key
           ).FirstOrDefault();

                if (conversationId > 0)
                    return (long)conversationId;

                var conversation = new Conversation
                {
                    Sujet = $"Conversation privée {membre1Id} & {membre2Id}",
                    IsGroup = 0,
                    CreatedAt = DateTime.Now
                };

                con.Conversations.Add(conversation);
                con.SaveChanges();

                con.ParticipantConversations.Add(new ParticipantConversation
                {
                    ConversationId = conversation.ConversationId,
                    MembreId = membre1Id,
                    JoinedAt = DateTime.Now
                });
                con.ParticipantConversations.Add(new ParticipantConversation
                {
                    ConversationId = conversation.ConversationId,
                    MembreId = membre2Id,
                    JoinedAt = DateTime.Now
                });
                con.SaveChanges();

                return conversation.ConversationId;
            }
            //// Si aucune conversation, FirstOrDefault retourne 0 (long default)
            //if (conversationId > 0)
            //{
            //    // Conversation trouvée, on retourne son ID
            //    return (long)conversationId;
            //}
            //else
            //{
            //    // Pas de conversation trouvée entre ces deux membres
            //    return 0;
            //}

        }

        public bool ParticipantExiste(long conversationId, long membreId)
        {
            using (var con = new Connection())
            {
                return con.ParticipantConversations
                          .Any(p => p.ConversationId == conversationId && p.MembreId == membreId);
            }
        }

        public int Supprimer(long conversationId)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationId);
                if (c != null)
                {
                    con.Conversations.Remove(c);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void ChargerConversations(DropDownList ddl, long membreId, string name = "")
        {
            using (Connection con = new Connection())
            {
                var query = from c in con.Conversations
                            join p in con.ParticipantConversations on c.ConversationId equals p.ConversationId
                            where p.MembreId == membreId
                            select new
                            {
                               id= c.ConversationId,
                               sujet= c.Sujet,
                               idgroup= c.IsGroup,
                               CreatedAt=c.CreatedAt,
                            };

                if (!string.IsNullOrEmpty(name))
                {
                    query = query.Where(x => x.sujet == name);
                }

                ddl.DataSource = query.ToList();
                ddl.DataTextField = "Sujet";
                ddl.DataValueField = "ConversationId";
                ddl.DataBind();
            }
        }

        public void ChargerParticipants(DropDownList ddl, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var participants = from p in con.ParticipantConversations
                                   join m in con.Membres on p.MembreId equals m.MembreId
                                   where p.ConversationId == conversationId
                                   select new
                                   {
                                       m.MembreId,
                                       NomComplet = m.Nom + " " + m.Prenom,
                                   };

                ddl.DataSource = participants.ToList();
                ddl.DataTextField = "NomComplet";
                ddl.DataValueField = "MembreId";
                ddl.DataBind();
            }
        }

        public int Creer(Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
               
                conversation.Sujet = conversationClass.Sujet;
                conversation.IsGroup = conversationClass.IsGroup;
                conversation.CreatedAt = conversationClass.CreatedAt;

                try
                {
                    con.Conversations.Add(conversation);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int Modifier(Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationClass.ConversationId);
                if (c != null)
                {
                    c.Sujet = conversationClass.Sujet;
                  
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int RetirerParticipant(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                var participant = con.ParticipantConversations.FirstOrDefault(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId);

                if (participant != null)
                {
                    try
                    {
                        con.ParticipantConversations.Remove(participant);
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public void ChargerParticipants(Repeater rpt, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var participants = from p in con.ParticipantConversations
                                   join m in con.Membres on p.MembreId equals m.MembreId
                                   where p.ConversationId == conversationId
                                   select new
                                   {
                                       m.MembreId,
                                       NomComplet = m.Nom + " " + m.Prenom,
                                      
                                   };

                rpt.DataSource = participants.ToList();
                rpt.DataBind();
            }
        }
        public List<long> ObtenirParticipants(long conversationId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var participants = con.ParticipantConversations
                                          .Where(pc => pc.ConversationId == conversationId)
                                          .Select(pc => (long)pc.MembreId)
                                          .ToList();

                    return participants;
                }
                catch (Exception ex)
                {
                    // Optionnel : log de l'erreur
                    return new List<long>();
                }
            }
        }



        public bool VerifierParticipation(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.ParticipantConversations.Any(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId);
            }
        }

        // Nouvelles méthodes pour les améliorations

        public void RechercherMembres(Repeater rpt, long membreConnecte, string recherche)
        {
            using (Connection con = new Connection())
            {
                var membres = from m in con.Membres
                              where m.MembreId != membreConnecte && // Exclure le membre connecté
                                    (string.IsNullOrEmpty(recherche) ||
                                     (m.Nom + " " + m.Prenom).Contains(recherche) ||
                                     m.Email.Contains(recherche))
                              select new
                              {
                                  id = m.MembreId,
                                  Membre = (m.Nom ?? "") + " " + (m.Prenom ?? ""),
                                  PhotoProfil = m.PhotoProfil ?? "default-avatar.png",
                                  Email = m.Email ?? "",
                                  Statut = "actif" // À adapter selon votre logique
                              };

                rpt.DataSource = membres.Take(20).ToList(); // Limiter à 20 résultats
                rpt.DataBind();
            }
        }

        public void ChargerConversationsRecentes(Repeater rpt, long membreId, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var conversations = from c in con.Conversations
                                    join pc in con.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                    where pc.MembreId == membreId
                                    orderby c.CreatedAt descending
                                    select new
                                    {
                                        ConversationId = c.ConversationId,
                                        Sujet = c.Sujet ?? "Conversation privée",
                                        IsGroup = c.IsGroup,
                                        CreatedAt = c.CreatedAt,
                                        // Récupérer le dernier message
                                        DernierMessage = con.Messages
                                            .Where(m => m.ConversationId == c.ConversationId)
                                            .OrderByDescending(m => m.DateEnvoi)
                                            .Select(m => m.Contenu)
                                            .FirstOrDefault() ?? "Aucun message",
                                        DateDernierMessage = con.Messages
                                            .Where(m => m.ConversationId == c.ConversationId)
                                            .OrderByDescending(m => m.DateEnvoi)
                                            .Select(m => m.DateEnvoi)
                                            .FirstOrDefault()
                                    };

                rpt.DataSource = conversations.Take(limite).ToList();
                rpt.DataBind();
            }
        }

        public bool VerifierAutorisationConversation(long membreId, long conversationId)
        {
            using (Connection con = new Connection())
            {
                return con.ParticipantConversations.Any(pc =>
                    pc.ConversationId == conversationId && pc.MembreId == membreId);
            }
        }
    }
}