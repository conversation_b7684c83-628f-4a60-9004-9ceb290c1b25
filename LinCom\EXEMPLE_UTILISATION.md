# 📖 Exemples d'Utilisation - Système de Messagerie LinCom

## 🎯 Résolution de l'Erreur ListView/Repeater

### **Problème Résolu**
```
Erreur: cannot convert from 'System.Web.UI.WebControls.ListView' to 'System.Web.UI.WebControls.Repeater'
```

### **✅ Solutions Disponibles**

## 🔧 Option 1 : Méthodes Spécifiques par Type

### **Pour Repeater**
```csharp
// Dans votre code-behind (.aspx.cs)
protected void ChargerMessagesRepeater()
{
    MessageImp objMessage = new MessageImp();
    long conversationId = 123;
    
    // Utiliser la méthode spécifique pour Repeater
    objMessage.ChargerMessages(monRepeater, conversationId, 20, 1);
}

protected void RechercherContactsRepeater()
{
    ConversationImp objConversation = new ConversationImp();
    long membreId = 456;
    string recherche = "Jean";
    
    // Utiliser la méthode spécifique pour Repeater
    objConversation.RechercherMembres(monRepeater, membreId, recherche);
}
```

### **Pour ListView**
```csharp
// Dans votre code-behind (.aspx.cs)
protected void ChargerMessagesListView()
{
    MessageImp objMessage = new MessageImp();
    long conversationId = 123;
    
    // Utiliser la méthode spécifique pour ListView
    objMessage.ChargerMessages(monListView, conversationId, 20, 1);
}

protected void RechercherContactsListView()
{
    ConversationImp objConversation = new ConversationImp();
    long membreId = 456;
    string recherche = "Jean";
    
    // Utiliser la méthode spécifique pour ListView
    objConversation.RechercherMembres(monListView, membreId, recherche);
}
```

## 🚀 Option 2 : Méthodes Génériques (Recommandé)

### **Avantages**
- ✅ Fonctionne avec tous types de contrôles
- ✅ Code plus flexible et maintenable
- ✅ Gestion d'erreur intégrée
- ✅ Messages d'état automatiques

### **Utilisation**
```csharp
// Dans votre code-behind (.aspx.cs)
protected void ChargerMessagesGenerique()
{
    MessageImp objMessage = new MessageImp();
    long conversationId = 123;
    
    // Fonctionne avec Repeater, ListView, GridView, DataList
    objMessage.ChargerMessagesGenerique(monControl, conversationId, 20, 1);
}

protected void RechercherContactsGenerique()
{
    ConversationImp objConversation = new ConversationImp();
    long membreId = 456;
    string recherche = "Jean";
    
    // Fonctionne avec tous types de contrôles
    objConversation.RechercherMembresGenerique(monControl, membreId, recherche);
}

protected void ChargerParticipantsGenerique()
{
    ConversationImp objConversation = new ConversationImp();
    long conversationId = 789;
    
    // Charger les participants dans n'importe quel contrôle
    objConversation.ChargerParticipantsGenerique(monControl, conversationId);
}
```

## 🛠️ Option 3 : Classe Utilitaire Directe

### **Pour un Contrôle Personnalisé**
```csharp
using LinCom.Imp;

protected void LierDonneesPersonnalisees()
{
    // Vos données
    var mesMessages = new List<object>
    {
        new { Id = 1, Contenu = "Bonjour", Expediteur = "Jean Dupont" },
        new { Id = 2, Contenu = "Salut", Expediteur = "Marie Martin" }
    };
    
    // Liaison sécurisée avec gestion d'erreur
    DataControlHelper.SafeBindData(monControl, mesMessages, "Aucun message disponible");
}

protected void VerifierTypeControle()
{
    // Vérifier si le contrôle est supporté
    if (DataControlHelper.IsSupportedDataControl(monControl))
    {
        string typeName = DataControlHelper.GetControlTypeName(monControl);
        Response.Write($"Contrôle supporté: {typeName}");
    }
    else
    {
        Response.Write("Contrôle non supporté");
    }
}
```

## 📋 Exemples Complets par Scénario

### **Scénario 1 : Page de Messagerie avec ListView**

#### **HTML (.aspx)**
```html
<asp:ListView ID="lvMessages" runat="server">
    <ItemTemplate>
        <div class="message">
            <strong><%# Eval("Expediteur") %></strong>
            <p><%# Eval("Contenu") %></p>
            <small><%# Eval("DateEnvoi") %></small>
        </div>
    </ItemTemplate>
    <EmptyDataTemplate>
        <p>Aucun message dans cette conversation.</p>
    </EmptyDataTemplate>
</asp:ListView>
```

#### **Code-behind (.aspx.cs)**
```csharp
protected void Page_Load(object sender, EventArgs e)
{
    if (!IsPostBack)
    {
        ChargerMessages();
    }
}

private void ChargerMessages()
{
    try
    {
        MessageImp objMessage = new MessageImp();
        long conversationId = GetConversationId(); // Votre logique
        
        // Option 1: Méthode spécifique
        objMessage.ChargerMessages(lvMessages, conversationId, 20, 1);
        
        // Option 2: Méthode générique (recommandé)
        // objMessage.ChargerMessagesGenerique(lvMessages, conversationId, 20, 1);
    }
    catch (Exception ex)
    {
        // Gestion d'erreur
        Response.Write($"<script>alert('Erreur: {ex.Message}');</script>");
    }
}
```

### **Scénario 2 : Recherche de Contacts avec Repeater**

#### **HTML (.aspx)**
```html
<asp:TextBox ID="txtRecherche" runat="server" placeholder="Rechercher un contact..." />
<asp:Button ID="btnRechercher" runat="server" Text="Rechercher" OnClick="btnRechercher_Click" />

<asp:Repeater ID="rptContacts" runat="server">
    <ItemTemplate>
        <div class="contact">
            <img src="<%# Eval("PhotoProfil") %>" alt="Photo" />
            <span><%# Eval("Membre") %></span>
            <small><%# Eval("Email") %></small>
        </div>
    </ItemTemplate>
</asp:Repeater>
```

#### **Code-behind (.aspx.cs)**
```csharp
protected void btnRechercher_Click(object sender, EventArgs e)
{
    try
    {
        ConversationImp objConversation = new ConversationImp();
        long membreConnecte = GetMembreConnecte(); // Votre logique
        string recherche = txtRecherche.Text.Trim();
        
        // Option 1: Méthode spécifique
        objConversation.RechercherMembres(rptContacts, membreConnecte, recherche);
        
        // Option 2: Méthode générique (recommandé)
        // objConversation.RechercherMembresGenerique(rptContacts, membreConnecte, recherche);
    }
    catch (Exception ex)
    {
        Response.Write($"<script>alert('Erreur de recherche: {ex.Message}');</script>");
    }
}
```

### **Scénario 3 : Gestion Dynamique du Type de Contrôle**

```csharp
protected void ChargerDonneesDynamique(object control, string typeData)
{
    try
    {
        // Vérifier le type de contrôle
        if (!DataControlHelper.IsSupportedDataControl(control))
        {
            throw new ArgumentException("Type de contrôle non supporté");
        }
        
        MessageImp objMessage = new MessageImp();
        ConversationImp objConversation = new ConversationImp();
        
        switch (typeData.ToLower())
        {
            case "messages":
                long conversationId = GetConversationId();
                objMessage.ChargerMessagesGenerique(control, conversationId, 20, 1);
                break;
                
            case "contacts":
                long membreId = GetMembreConnecte();
                string recherche = GetTermeRecherche();
                objConversation.RechercherMembresGenerique(control, membreId, recherche);
                break;
                
            case "participants":
                long convId = GetConversationId();
                objConversation.ChargerParticipantsGenerique(control, convId);
                break;
                
            default:
                DataControlHelper.SafeBindData(control, new List<object>(), "Type de données non reconnu");
                break;
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"Erreur ChargerDonneesDynamique: {ex.Message}");
        DataControlHelper.SafeBindData(control, new List<object>(), "Erreur lors du chargement");
    }
}
```

## 🎨 Bonnes Pratiques

### **1. Toujours Utiliser Try-Catch**
```csharp
try
{
    objMessage.ChargerMessagesGenerique(control, conversationId, 20, 1);
}
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"Erreur: {ex.Message}");
    // Afficher un message utilisateur approprié
}
```

### **2. Valider les Paramètres**
```csharp
private void ChargerMessages(object control, long conversationId)
{
    if (control == null)
        throw new ArgumentNullException(nameof(control));
        
    if (conversationId <= 0)
        throw new ArgumentException("ID de conversation invalide");
        
    if (!DataControlHelper.IsSupportedDataControl(control))
        throw new ArgumentException("Type de contrôle non supporté");
        
    // Logique...
}
```

### **3. Préférer les Méthodes Génériques**
```csharp
// ✅ Recommandé - Flexible et robuste
objMessage.ChargerMessagesGenerique(control, conversationId, 20, 1);

// ❌ Éviter - Rigide et source d'erreurs
objMessage.ChargerMessages((Repeater)control, conversationId, 20, 1);
```

## 📞 Support

### **En cas de Problème**
1. **Vérifiez** que le contrôle est supporté avec `DataControlHelper.IsSupportedDataControl()`
2. **Utilisez** les méthodes génériques (`*Generique`)
3. **Consultez** les logs de débogage dans Visual Studio
4. **Testez** avec la page `/test-messagerie.aspx`

### **Documentation**
- `GUIDE_RESOLUTION_ERREURS.md` - Guide de résolution d'erreurs
- `AMELIORATIONS_MESSAGERIE.md` - Documentation technique complète
- `README_VS2022.md` - Guide de démarrage rapide

---

**✅ Avec ces exemples, vous devriez pouvoir utiliser le système de messagerie avec n'importe quel type de contrôle sans erreur !**

**Date :** 2025-01-21  
**Version :** 1.0  
**Statut :** ✅ Testé et fonctionnel
