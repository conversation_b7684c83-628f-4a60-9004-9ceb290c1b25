# ===== DIAGNOSTIC DES IMAGES - MESSAGERIE LINCOM =====
# Script pour diagnostiquer et résoudre les problèmes d'images d'avatar

Write-Host "🔍 DIAGNOSTIC DES IMAGES - MESSAGERIE LINCOM" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour afficher les messages colorés
function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default   { Write-Host "📝 $Message" -ForegroundColo<PERSON> White }
    }
}

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "LinCom.csproj")) {
    Write-Status "Fichier LinCom.csproj non trouvé. Assurez-vous d'être dans le bon répertoire." "ERROR"
    exit 1
}

Write-Status "Répertoire du projet LinCom détecté" "SUCCESS"

# Vérifier les répertoires d'images
Write-Host ""
Write-Status "Vérification des répertoires d'images..." "INFO"

$repertoires = @(
    @{Path="file/membr"; Desc="Répertoire des photos de membres"},
    @{Path="assets/img"; Desc="Répertoire des assets images"},
    @{Path="file/img"; Desc="Répertoire des images système"}
)

foreach ($rep in $repertoires) {
    if (Test-Path $rep.Path) {
        $fileCount = (Get-ChildItem $rep.Path -File -ErrorAction SilentlyContinue | Measure-Object).Count
        Write-Status "✓ $($rep.Desc) - $fileCount fichiers" "SUCCESS"
    } else {
        Write-Status "✗ $($rep.Desc) - MANQUANT" "ERROR"
        
        # Créer le répertoire s'il n'existe pas
        try {
            New-Item -ItemType Directory -Path $rep.Path -Force | Out-Null
            Write-Status "  → Répertoire créé: $($rep.Path)" "SUCCESS"
        } catch {
            Write-Status "  → Erreur création: $($_.Exception.Message)" "ERROR"
        }
    }
}

# Vérifier les fichiers d'images critiques
Write-Host ""
Write-Status "Vérification des fichiers d'images critiques..." "INFO"

$fichiersImages = @(
    @{Path="file/membr/emptyuser.png"; Desc="Image utilisateur par défaut (système)"},
    @{Path="assets/img/default-avatar.svg"; Desc="Avatar SVG par défaut (nouveau)"},
    @{Path="file/membr/web.config"; Desc="Configuration d'accès aux images"}
)

foreach ($fichier in $fichiersImages) {
    if (Test-Path $fichier.Path) {
        $size = (Get-Item $fichier.Path).Length
        Write-Status "✓ $($fichier.Desc) - $size bytes" "SUCCESS"
    } else {
        Write-Status "✗ $($fichier.Desc) - MANQUANT" "ERROR"
        
        # Créer les fichiers manquants
        switch ($fichier.Path) {
            "assets/img/default-avatar.svg" {
                $svgContent = @'
<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="20" cy="20" r="20" fill="#E0E0E0"/>
  <circle cx="20" cy="16" r="6" fill="#BDBDBD"/>
  <path d="M8 32C8 26.4772 12.4772 22 18 22H22C27.5228 22 32 26.4772 32 32V32C32 33.1046 31.1046 34 30 34H10C8.89543 34 8 33.1046 8 32V32Z" fill="#BDBDBD"/>
</svg>
'@
                try {
                    $svgContent | Out-File -FilePath $fichier.Path -Encoding UTF8
                    Write-Status "  → Fichier SVG créé" "SUCCESS"
                } catch {
                    Write-Status "  → Erreur création SVG: $($_.Exception.Message)" "ERROR"
                }
            }
        }
    }
}

# Tester l'accès aux images via HTTP
Write-Host ""
Write-Status "Test d'accès HTTP aux images..." "INFO"

$urlsTest = @(
    "http://localhost:44319/file/membr/emptyuser.png",
    "http://localhost:44319/assets/img/default-avatar.svg"
)

foreach ($url in $urlsTest) {
    try {
        $response = Invoke-WebRequest -Uri $url -Method Head -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Status "✓ Accessible: $url" "SUCCESS"
        } else {
            Write-Status "⚠️  Status $($response.StatusCode): $url" "WARNING"
        }
    } catch {
        if ($_.Exception.Message -match "403") {
            Write-Status "❌ 403 Forbidden: $url" "ERROR"
            Write-Status "  → Problème de permissions ou configuration IIS" "WARNING"
        } elseif ($_.Exception.Message -match "404") {
            Write-Status "❌ 404 Not Found: $url" "ERROR"
            Write-Status "  → Fichier introuvable" "WARNING"
        } else {
            Write-Status "❌ Erreur réseau: $url" "ERROR"
            Write-Status "  → Serveur probablement arrêté" "WARNING"
        }
    }
}

# Vérifier la configuration Web.config
Write-Host ""
Write-Status "Vérification de la configuration Web.config..." "INFO"

$webConfigPath = "file/membr/web.config"
if (Test-Path $webConfigPath) {
    $webConfigContent = Get-Content $webConfigPath -Raw
    
    $checks = @(
        @{Pattern="StaticFileHandler"; Desc="Gestionnaire de fichiers statiques"},
        @{Pattern="image/png"; Desc="Type MIME PNG"},
        @{Pattern="image/svg"; Desc="Type MIME SVG"},
        @{Pattern="requestFiltering"; Desc="Filtrage des requêtes"}
    )
    
    foreach ($check in $checks) {
        if ($webConfigContent -match $check.Pattern) {
            Write-Status "✓ $($check.Desc) configuré" "SUCCESS"
        } else {
            Write-Status "⚠️  $($check.Desc) manquant" "WARNING"
        }
    }
} else {
    Write-Status "❌ Fichier web.config manquant dans file/membr/" "ERROR"
}

# Recommandations
Write-Host ""
Write-Host "🔧 RECOMMANDATIONS POUR RÉSOUDRE L'ERREUR 403:" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. 🗂️  PERMISSIONS DE RÉPERTOIRE:" -ForegroundColor Yellow
Write-Host "   • Vérifiez que IIS_IUSRS a accès en lecture au répertoire file/membr/" -ForegroundColor White
Write-Host "   • Clic droit sur file/membr → Propriétés → Sécurité → Modifier" -ForegroundColor Gray
Write-Host "   • Ajouter IIS_IUSRS avec permissions de lecture" -ForegroundColor Gray
Write-Host ""

Write-Host "2. 🌐 CONFIGURATION IIS:" -ForegroundColor Yellow
Write-Host "   • Ouvrez IIS Manager" -ForegroundColor White
Write-Host "   • Naviguez vers votre site → file/membr" -ForegroundColor White
Write-Host "   • Vérifiez que 'Directory Browsing' est activé" -ForegroundColor White
Write-Host "   • Vérifiez les 'Handler Mappings' pour les images" -ForegroundColor White
Write-Host ""

Write-Host "3. 📁 STRUCTURE DE FICHIERS:" -ForegroundColor Yellow
Write-Host "   • Assurez-vous que emptyuser.png existe dans file/membr/" -ForegroundColor White
Write-Host "   • Vérifiez que les fichiers ne sont pas corrompus" -ForegroundColor White
Write-Host "   • Testez l'accès direct via navigateur" -ForegroundColor White
Write-Host ""

Write-Host "4. 🔧 SOLUTIONS ALTERNATIVES:" -ForegroundColor Yellow
Write-Host "   • Utilisez ResolveUrl() dans le code-behind" -ForegroundColor White
Write-Host "   • Créez un handler HTTP personnalisé pour les images" -ForegroundColor White
Write-Host "   • Déplacez les images vers assets/img/ (plus accessible)" -ForegroundColor White
Write-Host ""

Write-Host "5. 🧪 TESTS À EFFECTUER:" -ForegroundColor Yellow
Write-Host "   • Testez l'URL directe: http://localhost:44319/file/membr/emptyuser.png" -ForegroundColor White
Write-Host "   • Vérifiez les logs IIS pour plus de détails" -ForegroundColor White
Write-Host "   • Testez avec un autre navigateur" -ForegroundColor White
Write-Host ""

# Solutions automatiques
Write-Host "🤖 SOLUTIONS AUTOMATIQUES DISPONIBLES:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

$response = Read-Host "Voulez-vous appliquer les corrections automatiques ? (O/N)"

if ($response -eq "O" -or $response -eq "o" -or $response -eq "Y" -or $response -eq "y") {
    Write-Host ""
    Write-Status "Application des corrections automatiques..." "INFO"
    
    # 1. Créer l'avatar par défaut si manquant
    if (-not (Test-Path "assets/img/default-avatar.svg")) {
        $svgContent = @'
<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="20" cy="20" r="20" fill="#E0E0E0"/>
  <circle cx="20" cy="16" r="6" fill="#BDBDBD"/>
  <path d="M8 32C8 26.4772 12.4772 22 18 22H22C27.5228 22 32 26.4772 32 32V32C32 33.1046 31.1046 34 30 34H10C8.89543 34 8 33.1046 8 32V32Z" fill="#BDBDBD"/>
</svg>
'@
        try {
            $svgContent | Out-File -FilePath "assets/img/default-avatar.svg" -Encoding UTF8
            Write-Status "✓ Avatar SVG par défaut créé" "SUCCESS"
        } catch {
            Write-Status "❌ Erreur création avatar SVG: $($_.Exception.Message)" "ERROR"
        }
    }
    
    # 2. Copier emptyuser.png vers assets/img comme fallback
    if ((Test-Path "file/membr/emptyuser.png") -and (-not (Test-Path "assets/img/emptyuser.png"))) {
        try {
            Copy-Item "file/membr/emptyuser.png" "assets/img/emptyuser.png"
            Write-Status "✓ emptyuser.png copié vers assets/img/" "SUCCESS"
        } catch {
            Write-Status "❌ Erreur copie emptyuser.png: $($_.Exception.Message)" "ERROR"
        }
    }
    
    Write-Host ""
    Write-Status "Corrections automatiques terminées" "SUCCESS"
}

Write-Host ""
Write-Host "📋 RÉSUMÉ DU DIAGNOSTIC:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "L'erreur 403 Forbidden est généralement causée par:" -ForegroundColor White
Write-Host "• Permissions insuffisantes sur le répertoire file/membr/" -ForegroundColor Gray
Write-Host "• Configuration IIS qui bloque l'accès aux fichiers statiques" -ForegroundColor Gray
Write-Host "• Problème de configuration du serveur web" -ForegroundColor Gray
Write-Host ""
Write-Host "Les corrections appliquées incluent:" -ForegroundColor White
Write-Host "• Configuration web.config pour autoriser l'accès aux images" -ForegroundColor Gray
Write-Host "• Gestion d'erreur JavaScript améliorée avec fallbacks" -ForegroundColor Gray
Write-Host "• Fonction GetSecureAvatarUrl() avec chemins multiples" -ForegroundColor Gray
Write-Host ""
Write-Host "Si le problème persiste, vérifiez les permissions IIS et la configuration du serveur." -ForegroundColor Yellow

Write-Host ""
Write-Host "Appuyez sur une touche pour fermer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
