# ===== SCRIPT DE VÉRIFICATION - CORRECTIONS MESSAGERIE MODERNE =====
# Ce script vérifie que toutes les corrections ont été appliquées correctement

Write-Host "🔧 VÉRIFICATION DES CORRECTIONS - MESSAG<PERSON><PERSON> MODERNE" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour afficher les messages colorés
function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default   { Write-Host "📝 $Message" -ForegroundColor White }
    }
}

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "LinCom.csproj")) {
    Write-Status "Fichier LinCom.csproj non trouvé. Assurez-vous d'être dans le bon répertoire." "ERROR"
    exit 1
}

Write-Status "Vérification des corrections appliquées..." "INFO"
Write-Host ""

# 1. Vérifier les fichiers de la messagerie moderne
Write-Status "1. Vérification des fichiers principaux" "INFO"

$fichiersRequis = @(
    @{Path="messagerie-moderne.aspx"; Desc="Page principale moderne"},
    @{Path="messagerie-moderne.aspx.cs"; Desc="Code-behind"},
    @{Path="messagerie-moderne.aspx.designer.cs"; Desc="Designer"},
    @{Path="assets/css/messagerie-moderne.css"; Desc="Styles principaux"},
    @{Path="assets/css/messagerie-animations.css"; Desc="Animations"},
    @{Path="assets/js/messagerie-moderne.js"; Desc="JavaScript"}
)

$fichiersOK = 0
foreach ($fichier in $fichiersRequis) {
    if (Test-Path $fichier.Path) {
        Write-Status "   ✓ $($fichier.Desc)" "SUCCESS"
        $fichiersOK++
    } else {
        Write-Status "   ✗ $($fichier.Desc) - $($fichier.Path)" "ERROR"
    }
}

Write-Host ""

# 2. Vérifier les corrections dans ConversationImp.cs
Write-Status "2. Vérification des méthodes ajoutées dans ConversationImp.cs" "INFO"

$conversationImpContent = Get-Content "LinCom/Imp/ConversationImp.cs" -Raw

$methodesRequises = @(
    @{Method="public int Ajouter"; Desc="Méthode alias Ajouter"},
    @{Method="public long ObtenirConversationPrivee"; Desc="Obtenir conversation privée"},
    @{Method="public long CreerEtRetournerID"; Desc="Créer et retourner ID"}
)

$methodesOK = 0
foreach ($methode in $methodesRequises) {
    if ($conversationImpContent -match [regex]::Escape($methode.Method)) {
        Write-Status "   ✓ $($methode.Desc)" "SUCCESS"
        $methodesOK++
    } else {
        Write-Status "   ✗ $($methode.Desc)" "ERROR"
    }
}

Write-Host ""

# 3. Vérifier l'interface IConversation.cs
Write-Status "3. Vérification de l'interface IConversation.cs" "INFO"

$interfaceContent = Get-Content "LinCom/Imp/IConversation.cs" -Raw

$signaturesRequises = @(
    @{Signature="int Ajouter(Conversation_Class"; Desc="Signature Ajouter"},
    @{Signature="long ObtenirConversationPrivee(long"; Desc="Signature ObtenirConversationPrivee"},
    @{Signature="long CreerEtRetournerID(Conversation_Class"; Desc="Signature CreerEtRetournerID"}
)

$signaturesOK = 0
foreach ($signature in $signaturesRequises) {
    if ($interfaceContent -match [regex]::Escape($signature.Signature)) {
        Write-Status "   ✓ $($signature.Desc)" "SUCCESS"
        $signaturesOK++
    } else {
        Write-Status "   ✗ $($signature.Desc)" "ERROR"
    }
}

Write-Host ""

# 4. Vérifier les corrections dans messagerie-moderne.aspx.cs
Write-Status "4. Vérification des corrections dans messagerie-moderne.aspx.cs" "INFO"

$codeContent = Get-Content "LinCom/messagerie-moderne.aspx.cs" -Raw

$correctionsRequises = @(
    @{Pattern="IsGroup = 0"; Desc="Correction type IsGroup (int au lieu de bool)"},
    @{Pattern="ObtenirConversationPrivee"; Desc="Utilisation méthode ObtenirConversationPrivee"},
    @{Pattern="CreerEtRetournerID"; Desc="Utilisation méthode CreerEtRetournerID"},
    @{Pattern="using LinCom.Model"; Desc="Import LinCom.Model ajouté"}
)

$correctionsOK = 0
foreach ($correction in $correctionsRequises) {
    if ($codeContent -match [regex]::Escape($correction.Pattern)) {
        Write-Status "   ✓ $($correction.Desc)" "SUCCESS"
        $correctionsOK++
    } else {
        Write-Status "   ✗ $($correction.Desc)" "ERROR"
    }
}

Write-Host ""

# 5. Vérifier que les erreurs ont été supprimées
Write-Status "5. Vérification de l'absence d'erreurs connues" "INFO"

$erreursCorrigees = @(
    @{Pattern="CreatedBy ="; Desc="Propriété CreatedBy supprimée"},
    @{Pattern="IsGroup = false"; Desc="Type bool corrigé vers int"}
)

$erreursAbsentes = 0
foreach ($erreur in $erreursCorrigees) {
    if ($codeContent -notmatch [regex]::Escape($erreur.Pattern)) {
        Write-Status "   ✓ $($erreur.Desc)" "SUCCESS"
        $erreursAbsentes++
    } else {
        Write-Status "   ✗ $($erreur.Desc) - Erreur encore présente" "ERROR"
    }
}

Write-Host ""

# 6. Vérifier les guides de documentation
Write-Status "6. Vérification de la documentation" "INFO"

$guidesRequis = @(
    @{Path="GUIDE_MESSAGERIE_MODERNE.md"; Desc="Guide principal"},
    @{Path="GUIDE_ERREURS_MESSAGERIE_MODERNE.md"; Desc="Guide des erreurs"},
    @{Path="COMPARAISON_INTERFACES.md"; Desc="Comparaison interfaces"}
)

$guidesOK = 0
foreach ($guide in $guidesRequis) {
    if (Test-Path $guide.Path) {
        Write-Status "   ✓ $($guide.Desc)" "SUCCESS"
        $guidesOK++
    } else {
        Write-Status "   ✗ $($guide.Desc)" "ERROR"
    }
}

Write-Host ""

# 7. Résumé des vérifications
Write-Status "📊 RÉSUMÉ DES VÉRIFICATIONS" "INFO"
Write-Host ""

$totalVerifications = $fichiersRequis.Count + $methodesRequises.Count + $signaturesRequises.Count + $correctionsRequises.Count + $erreursCorrigees.Count + $guidesRequis.Count
$totalReussies = $fichiersOK + $methodesOK + $signaturesOK + $correctionsOK + $erreursAbsentes + $guidesOK

Write-Host "Fichiers principaux:        $fichiersOK/$($fichiersRequis.Count)" -ForegroundColor $(if($fichiersOK -eq $fichiersRequis.Count) {"Green"} else {"Red"})
Write-Host "Méthodes ConversationImp:   $methodesOK/$($methodesRequises.Count)" -ForegroundColor $(if($methodesOK -eq $methodesRequises.Count) {"Green"} else {"Red"})
Write-Host "Signatures IConversation:   $signaturesOK/$($signaturesRequises.Count)" -ForegroundColor $(if($signaturesOK -eq $signaturesRequises.Count) {"Green"} else {"Red"})
Write-Host "Corrections code-behind:    $correctionsOK/$($correctionsRequises.Count)" -ForegroundColor $(if($correctionsOK -eq $correctionsRequises.Count) {"Green"} else {"Red"})
Write-Host "Erreurs supprimées:         $erreursAbsentes/$($erreursCorrigees.Count)" -ForegroundColor $(if($erreursAbsentes -eq $erreursCorrigees.Count) {"Green"} else {"Red"})
Write-Host "Documentation:              $guidesOK/$($guidesRequis.Count)" -ForegroundColor $(if($guidesOK -eq $guidesRequis.Count) {"Green"} else {"Red"})

Write-Host ""
Write-Host "TOTAL: $totalReussies/$totalVerifications vérifications réussies" -ForegroundColor $(if($totalReussies -eq $totalVerifications) {"Green"} else {"Yellow"})

Write-Host ""

# 8. Recommandations
if ($totalReussies -eq $totalVerifications) {
    Write-Host "🎉 TOUTES LES CORRECTIONS SONT APPLIQUÉES !" -ForegroundColor Green
    Write-Host "============================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Votre messagerie moderne est prête à être utilisée !" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Prochaines étapes recommandées:" -ForegroundColor Cyan
    Write-Host "   1. Compiler le projet (Ctrl+Shift+B)" -ForegroundColor White
    Write-Host "   2. Lancer l'application (F5)" -ForegroundColor White
    Write-Host "   3. Tester la messagerie moderne (/messagerie-moderne.aspx)" -ForegroundColor White
    Write-Host "   4. Utiliser le script: .\TesterMessagerie.ps1" -ForegroundColor White
} else {
    Write-Host "⚠️  CORRECTIONS INCOMPLÈTES" -ForegroundColor Yellow
    Write-Host "===========================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Certaines corrections ne sont pas encore appliquées." -ForegroundColor Yellow
    Write-Host "Veuillez vérifier les éléments marqués en rouge ci-dessus." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📚 Consultez les guides pour plus d'informations:" -ForegroundColor Cyan
    Write-Host "   • GUIDE_ERREURS_MESSAGERIE_MODERNE.md" -ForegroundColor White
    Write-Host "   • GUIDE_RESOLUTION_ERREURS.md" -ForegroundColor White
}

Write-Host ""

# 9. Test de compilation rapide (optionnel)
Write-Host "🔨 Test de compilation rapide..." -ForegroundColor Cyan

try {
    # Rechercher MSBuild
    $msbuildPath = ""
    $possiblePaths = @(
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $msbuildPath = $path
            break
        }
    }
    
    if ($msbuildPath) {
        Write-Status "MSBuild trouvé, test de compilation..." "INFO"
        $output = & $msbuildPath "LinCom.csproj" /p:Configuration=Debug /verbosity:quiet /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "✅ Compilation réussie - Aucune erreur détectée" "SUCCESS"
        } else {
            Write-Status "❌ Erreurs de compilation détectées" "ERROR"
            Write-Host "Sortie MSBuild:" -ForegroundColor Yellow
            Write-Host $output -ForegroundColor Red
        }
    } else {
        Write-Status "MSBuild non trouvé - Utilisez Visual Studio pour compiler" "WARNING"
    }
}
catch {
    Write-Status "Impossible de tester la compilation: $($_.Exception.Message)" "WARNING"
}

Write-Host ""
Write-Status "Vérification terminée" "INFO"

# Pause pour permettre la lecture
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
