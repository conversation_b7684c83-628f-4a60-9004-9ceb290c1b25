﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IConversation
    {
        void AfficherDetails(long conversationId, Conversation_Class conversation);
        int Creer(Conversation_Class conversation);
        int Modifier(Conversation_Class conversation);
        int Supprimer(long conversationId);
        void ChargerConversations(DropDownList ddl, long membreId, string name = "");
        void ChargerParticipants(DropDownList ddl, long conversationId);
        int AjouterParticipant(long conversationId, long membreId);
        int RetirerParticipant(long conversationId, long membreId);
        bool VerifierParticipation(long conversationId, long membreId);
        void ChargerParticipants(Repeater rpt, long conversationId);
        long VerifierConversationId(long membre1Id, long membre2Id);
        bool ParticipantExiste(long conversationId, long membreId);
        List<long> ObtenirParticipants(long conversationId);

        // Nouvelles méthodes pour les améliorations
        void RechercherMembres(Repeater rpt, long membreConnecte, string recherche);
        void RechercherMembres(ListView lv, long membreConnecte, string recherche);
        void ChargerConversationsRecentes(Repeater rpt, long membreId, int limite = 10);
        void ChargerConversationsRecentes(ListView lv, long membreId, int limite = 10);
        void ChargerParticipants(ListView lv, long conversationId);
        bool VerifierAutorisationConversation(long membreId, long conversationId);

        // Méthodes génériques pour tous types de contrôles
        void RechercherMembresGenerique(object control, long membreConnecte, string recherche);
        void ChargerConversationsRecentesGenerique(object control, long membreId, int limite = 10);
        void ChargerParticipantsGenerique(object control, long conversationId);
    }
}
