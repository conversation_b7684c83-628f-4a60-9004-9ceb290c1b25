using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Web.UI;

namespace LinCom
{
    public partial class test_messagerie : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                litTestResults.Text = "<p class='test-info'>Cliquez sur les boutons pour tester les différentes fonctionnalités.</p>";
            }
        }

        protected void btnTestValidation_Click(object sender, EventArgs e)
        {
            try
            {
                string message = txtTestMessage.Text;
                bool isValid = SecurityHelper.ValidateMessage(message);
                
                if (isValid)
                {
                    validationResult.InnerHtml = "<div class='test-success'>✅ Message valide</div>";
                }
                else
                {
                    validationResult.InnerHtml = "<div class='test-error'>❌ Message invalide</div>";
                }
                
                // Ajouter des détails
                StringBuilder details = new StringBuilder();
                details.Append("<div class='code-block'>");
                details.Append($"Longueur: {message.Length} caractères<br/>");
                details.Append($"Vide: {string.IsNullOrWhiteSpace(message)}<br/>");
                details.Append($"Trop long: {message.Length > 5000}<br/>");
                details.Append("</div>");
                
                validationResult.InnerHtml += details.ToString();
            }
            catch (Exception ex)
            {
                validationResult.InnerHtml = $"<div class='test-error'>Erreur: {ex.Message}</div>";
            }
        }

        protected void btnTestSanitize_Click(object sender, EventArgs e)
        {
            try
            {
                string input = txtTestHtml.Text;
                string sanitized = SecurityHelper.SanitizeHtml(input);
                
                StringBuilder result = new StringBuilder();
                result.Append("<div class='test-success'>✅ Sanitisation effectuée</div>");
                result.Append("<div class='code-block'>");
                result.Append($"<strong>Avant:</strong> {System.Web.HttpUtility.HtmlEncode(input)}<br/>");
                result.Append($"<strong>Après:</strong> {System.Web.HttpUtility.HtmlEncode(sanitized)}");
                result.Append("</div>");
                
                sanitizeResult.InnerHtml = result.ToString();
            }
            catch (Exception ex)
            {
                sanitizeResult.InnerHtml = $"<div class='test-error'>Erreur: {ex.Message}</div>";
            }
        }

        protected void btnTestCache_Click(object sender, EventArgs e)
        {
            try
            {
                Stopwatch sw = Stopwatch.StartNew();
                
                // Test d'écriture dans le cache
                string testKey = "test_cache_" + DateTime.Now.Ticks;
                string testValue = "Valeur de test " + DateTime.Now;
                
                CacheHelper.Set(testKey, testValue, 1);
                
                // Test de lecture
                string cachedValue = CacheHelper.Get<string>(testKey);
                
                sw.Stop();
                
                StringBuilder result = new StringBuilder();
                if (cachedValue == testValue)
                {
                    result.Append("<div class='test-success'>✅ Cache fonctionne correctement</div>");
                }
                else
                {
                    result.Append("<div class='test-error'>❌ Problème avec le cache</div>");
                }
                
                result.Append("<div class='code-block'>");
                result.Append($"Temps d'exécution: {sw.ElapsedMilliseconds} ms<br/>");
                result.Append($"Clé: {testKey}<br/>");
                result.Append($"Valeur stockée: {testValue}<br/>");
                result.Append($"Valeur récupérée: {cachedValue}");
                result.Append("</div>");
                
                cacheResult.InnerHtml = result.ToString();
                
                // Nettoyer
                CacheHelper.Remove(testKey);
            }
            catch (Exception ex)
            {
                cacheResult.InnerHtml = $"<div class='test-error'>Erreur: {ex.Message}</div>";
            }
        }

        protected void btnCacheStats_Click(object sender, EventArgs e)
        {
            try
            {
                var stats = CacheHelper.GetCacheStats();
                
                StringBuilder result = new StringBuilder();
                result.Append("<div class='test-info'>📊 Statistiques du Cache</div>");
                result.Append("<div class='code-block'>");
                
                foreach (var stat in stats)
                {
                    result.Append($"{stat.Key}: {stat.Value}<br/>");
                }
                
                result.Append("</div>");
                
                statsResult.InnerHtml = result.ToString();
            }
            catch (Exception ex)
            {
                statsResult.InnerHtml = $"<div class='test-error'>Erreur: {ex.Message}</div>";
            }
        }

        protected void btnTestRateLimit_Click(object sender, EventArgs e)
        {
            try
            {
                long testUserId = 999; // ID de test
                int successCount = 0;
                int blockedCount = 0;
                
                // Tester 10 requêtes rapides
                for (int i = 0; i < 10; i++)
                {
                    if (SecurityHelper.CheckRateLimit(testUserId, 5, 1)) // 5 requêtes par minute max
                    {
                        successCount++;
                    }
                    else
                    {
                        blockedCount++;
                    }
                }
                
                StringBuilder result = new StringBuilder();
                result.Append("<div class='test-info'>🚦 Test de Rate Limiting</div>");
                result.Append("<div class='code-block'>");
                result.Append($"Requêtes autorisées: {successCount}<br/>");
                result.Append($"Requêtes bloquées: {blockedCount}<br/>");
                result.Append($"Limite configurée: 5 requêtes par minute");
                result.Append("</div>");
                
                if (blockedCount > 0)
                {
                    result.Append("<div class='test-success'>✅ Rate limiting fonctionne</div>");
                }
                else
                {
                    result.Append("<div class='test-warning'>⚠️ Toutes les requêtes ont été autorisées</div>");
                }
                
                rateLimitResult.InnerHtml = result.ToString();
            }
            catch (Exception ex)
            {
                rateLimitResult.InnerHtml = $"<div class='test-error'>Erreur: {ex.Message}</div>";
            }
        }

        protected void btnTestSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtTestSearch.Text;
                string cleanedTerm = SecurityHelper.CleanSearchTerm(searchTerm);
                bool hasSqlInjection = SecurityHelper.ContainsSqlInjection(searchTerm);
                
                StringBuilder result = new StringBuilder();
                result.Append("<div class='test-info'>🔍 Test de Recherche</div>");
                result.Append("<div class='code-block'>");
                result.Append($"Terme original: {System.Web.HttpUtility.HtmlEncode(searchTerm)}<br/>");
                result.Append($"Terme nettoyé: {System.Web.HttpUtility.HtmlEncode(cleanedTerm)}<br/>");
                result.Append($"Contient SQL injection: {(hasSqlInjection ? "Oui" : "Non")}");
                result.Append("</div>");
                
                if (hasSqlInjection)
                {
                    result.Append("<div class='test-error'>❌ Terme de recherche dangereux détecté</div>");
                }
                else
                {
                    result.Append("<div class='test-success'>✅ Terme de recherche sécurisé</div>");
                }
                
                searchResult.InnerHtml = result.ToString();
            }
            catch (Exception ex)
            {
                searchResult.InnerHtml = $"<div class='test-error'>Erreur: {ex.Message}</div>";
            }
        }
    }
}
