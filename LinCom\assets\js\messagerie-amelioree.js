/**
 * MESSAGERIE AMÉLIORÉE - JavaScript
 * Fonctionnalités optimisées pour la messagerie LinCom
 */

// Configuration globale
const MessageriConfig = {
    SEARCH_DELAY: 300,
    AUTO_REFRESH_INTERVAL: 30000, // 30 secondes
    MAX_SEARCH_LENGTH: 100,
    DEBOUNCE_DELAY: 250
};

// Variables globales
let searchTimeout = null;
let refreshInterval = null;
let currentContactId = null;
let isSearchActive = false;

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    initializeMessagerie();
    setupEventListeners();
    startAutoRefresh();
});

/**
 * Initialisation de la messagerie
 */
function initializeMessagerie() {
    console.log('🚀 Initialisation de la messagerie améliorée');
    
    // Mettre à jour le compteur de contacts
    updateContactsCount();
    
    // Initialiser les statuts de connexion
    updateConnectionStatus();
    
    // Configurer les tooltips
    setupTooltips();
    
    // Restaurer l'état de la recherche si nécessaire
    restoreSearchState();
}

/**
 * Configuration des écouteurs d'événements
 */
function setupEventListeners() {
    // Recherche améliorée avec debounce
    const searchInput = document.getElementById('txtRechercheContact');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            debounce(function() {
                rechercherContactsAmeliore(e.target.value);
            }, MessageriConfig.DEBOUNCE_DELAY)();
        });
        
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                clearSearch();
            }
        });
    }
    
    // Gestion des erreurs d'images
    setupImageErrorHandling();
    
    // Gestion du redimensionnement
    window.addEventListener('resize', debounce(handleResize, 250));
}

/**
 * Recherche de contacts améliorée
 */
function rechercherContactsAmeliore(terme) {
    const searchInput = document.getElementById('txtRechercheContact');
    const clearButton = document.querySelector('.clear-search');
    const resultsInfo = document.getElementById('searchResultsInfo');
    const contacts = document.querySelectorAll('.contact-item');
    
    // Validation de l'entrée
    if (terme.length > MessageriConfig.MAX_SEARCH_LENGTH) {
        terme = terme.substring(0, MessageriConfig.MAX_SEARCH_LENGTH);
        searchInput.value = terme;
    }
    
    // Gestion de l'état de recherche
    isSearchActive = terme.length > 0;
    
    if (isSearchActive) {
        clearButton.style.display = 'block';
        resultsInfo.style.display = 'block';
    } else {
        clearButton.style.display = 'none';
        resultsInfo.style.display = 'none';
        // Réafficher tous les contacts
        contacts.forEach(contact => {
            contact.style.display = 'flex';
            removeHighlight(contact);
        });
        return;
    }
    
    // Effectuer la recherche
    const searchTerm = terme.toLowerCase().trim();
    let visibleCount = 0;
    
    contacts.forEach(contact => {
        const contactName = contact.querySelector('.contact-name');
        const contactStatus = contact.querySelector('.contact-status');
        
        if (contactName) {
            const name = contactName.textContent.toLowerCase();
            const status = contactStatus ? contactStatus.textContent.toLowerCase() : '';
            
            if (name.includes(searchTerm) || status.includes(searchTerm)) {
                contact.style.display = 'flex';
                highlightSearchTerm(contact, searchTerm);
                visibleCount++;
            } else {
                contact.style.display = 'none';
                removeHighlight(contact);
            }
        }
    });
    
    // Mettre à jour les informations de résultats
    updateSearchResults(visibleCount, terme);
}

/**
 * Surligner les termes de recherche
 */
function highlightSearchTerm(contactElement, searchTerm) {
    const nameElement = contactElement.querySelector('.contact-name');
    if (nameElement && searchTerm.length > 0) {
        const originalText = nameElement.getAttribute('data-original-text') || nameElement.textContent;
        nameElement.setAttribute('data-original-text', originalText);
        
        const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
        const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
        nameElement.innerHTML = highlightedText;
    }
}

/**
 * Supprimer le surlignage
 */
function removeHighlight(contactElement) {
    const nameElement = contactElement.querySelector('.contact-name');
    if (nameElement) {
        const originalText = nameElement.getAttribute('data-original-text');
        if (originalText) {
            nameElement.textContent = originalText;
            nameElement.removeAttribute('data-original-text');
        }
    }
}

/**
 * Échapper les caractères spéciaux pour regex
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Mettre à jour les résultats de recherche
 */
function updateSearchResults(count, searchTerm) {
    const resultsInfo = document.getElementById('searchResultsInfo');
    if (resultsInfo) {
        if (count === 0) {
            resultsInfo.innerHTML = `<i class="fas fa-exclamation-circle"></i> Aucun résultat pour "${searchTerm}"`;
            resultsInfo.className = 'search-results-info no-results';
        } else {
            resultsInfo.innerHTML = `<i class="fas fa-search"></i> ${count} contact${count > 1 ? 's' : ''} trouvé${count > 1 ? 's' : ''}`;
            resultsInfo.className = 'search-results-info has-results';
        }
    }
}

/**
 * Effacer la recherche
 */
function clearSearch() {
    const searchInput = document.getElementById('txtRechercheContact');
    const clearButton = document.querySelector('.clear-search');
    const resultsInfo = document.getElementById('searchResultsInfo');
    
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }
    
    if (clearButton) {
        clearButton.style.display = 'none';
    }
    
    if (resultsInfo) {
        resultsInfo.style.display = 'none';
    }
    
    // Réafficher tous les contacts et supprimer le surlignage
    const contacts = document.querySelectorAll('.contact-item');
    contacts.forEach(contact => {
        contact.style.display = 'flex';
        removeHighlight(contact);
    });
    
    isSearchActive = false;
}

/**
 * Mettre à jour le compteur de contacts
 */
function updateContactsCount() {
    const contacts = document.querySelectorAll('.contact-item');
    const countElement = document.getElementById('contactsCount');
    
    if (countElement) {
        const count = contacts.length;
        countElement.textContent = count;
        countElement.title = `${count} contact${count > 1 ? 's' : ''} disponible${count > 1 ? 's' : ''}`;
    }
}

/**
 * Mettre à jour le statut de connexion
 */
function updateConnectionStatus() {
    const statusElement = document.getElementById('connectionStatus');
    if (statusElement) {
        // Simuler la vérification de connexion
        const isOnline = navigator.onLine;
        const statusIcon = statusElement.querySelector('i');
        
        if (isOnline) {
            statusElement.innerHTML = '<i class="fas fa-circle"></i> En ligne';
            statusElement.style.color = 'var(--success-color)';
        } else {
            statusElement.innerHTML = '<i class="fas fa-circle"></i> Hors ligne';
            statusElement.style.color = 'var(--accent-color)';
        }
    }
}

/**
 * Actualiser les messages
 */
function refreshMessages() {
    const refreshBtn = document.querySelector('.btn-refresh');
    const refreshIcon = refreshBtn ? refreshBtn.querySelector('i') : null;
    
    if (refreshIcon) {
        refreshIcon.classList.add('fa-spin');
    }
    
    // Simuler le rechargement
    setTimeout(() => {
        if (refreshIcon) {
            refreshIcon.classList.remove('fa-spin');
        }
        
        // Déclencher un postback pour actualiser les données
        if (typeof __doPostBack === 'function') {
            __doPostBack('refreshMessages', '');
        } else {
            // Fallback : recharger la page
            window.location.reload();
        }
    }, 1000);
}

/**
 * Démarrer l'actualisation automatique
 */
function startAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    
    refreshInterval = setInterval(() => {
        updateConnectionStatus();
        // Autres vérifications périodiques peuvent être ajoutées ici
    }, MessageriConfig.AUTO_REFRESH_INTERVAL);
}

/**
 * Arrêter l'actualisation automatique
 */
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

/**
 * Gestion des erreurs d'images
 */
function setupImageErrorHandling() {
    const images = document.querySelectorAll('.contact-avatar img, .message-avatar img');
    images.forEach(img => {
        if (!img.hasAttribute('data-error-handled')) {
            img.addEventListener('error', function() {
                console.log('Erreur de chargement d\'image:', this.src);

                // Essayer différentes images de fallback
                const fallbacks = [
                    'file/membr/emptyuser.png',
                    'assets/img/default-avatar.svg',
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSIyMCIgZmlsbD0iI0UwRTBFMCIvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjYiIGZpbGw9IiNCREJEQkQiLz4KICA8cGF0aCBkPSJNOCAzMkM4IDI2LjQ3NzIgMTIuNDc3MiAyMiAxOCAyMkgyMkMyNy41MjI4IDIyIDMyIDI2LjQ3NzIgMzIgMzJWMzJDMzIgMzMuMTA0NiAzMS4xMDQ2IDM0IDMwIDM0SDEwQzguODk1NDMgMzQgOCAzMy4xMDQ2IDggMzJWMzJaIiBmaWxsPSIjQkRCREJEIi8+Cjwvc3ZnPgo='
                ];

                const currentSrc = this.src;
                let nextFallback = null;

                for (let i = 0; i < fallbacks.length; i++) {
                    if (!currentSrc.includes(fallbacks[i])) {
                        nextFallback = fallbacks[i];
                        break;
                    }
                }

                if (nextFallback) {
                    this.src = nextFallback;
                    this.alt = 'Avatar par défaut';
                } else {
                    // En dernier recours, masquer l'image et afficher une icône
                    this.style.display = 'none';
                    const parent = this.parentElement;
                    if (parent && !parent.querySelector('.avatar-fallback')) {
                        const fallbackIcon = document.createElement('div');
                        fallbackIcon.className = 'avatar-fallback';
                        fallbackIcon.innerHTML = '<i class="fas fa-user"></i>';
                        fallbackIcon.style.cssText = 'width: 40px; height: 40px; background: #E0E0E0; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #BDBDBD;';
                        parent.appendChild(fallbackIcon);
                    }
                }
            });
            img.setAttribute('data-error-handled', 'true');
        }
    });
}

/**
 * Configuration des tooltips
 */
function setupTooltips() {
    const elementsWithTooltip = document.querySelectorAll('[title]');
    elementsWithTooltip.forEach(element => {
        // Améliorer l'accessibilité des tooltips
        element.setAttribute('aria-label', element.title);
    });
}

/**
 * Fonction debounce pour optimiser les performances
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Gestion du redimensionnement
 */
function handleResize() {
    // Ajuster l'interface selon la taille de l'écran
    const chatWrapper = document.querySelector('.chat-wrapper');
    if (chatWrapper && window.innerWidth <= 768) {
        chatWrapper.classList.add('mobile-view');
    } else if (chatWrapper) {
        chatWrapper.classList.remove('mobile-view');
    }
}

/**
 * Restaurer l'état de la recherche
 */
function restoreSearchState() {
    const searchInput = document.getElementById('txtRechercheContact');
    if (searchInput && searchInput.value.length > 0) {
        rechercherContactsAmeliore(searchInput.value);
    }
}

/**
 * Sélectionner un contact
 */
function selectContact(contactId, contactName) {
    currentContactId = contactId;
    
    // Mettre à jour l'interface
    const contacts = document.querySelectorAll('.contact-item');
    contacts.forEach(contact => {
        contact.classList.remove('active');
    });
    
    const selectedContact = document.querySelector(`[data-contact-id="${contactId}"]`);
    if (selectedContact) {
        selectedContact.classList.add('active');
    }
    
    console.log(`Contact sélectionné: ${contactName} (ID: ${contactId})`);
}

/**
 * Nettoyage lors de la fermeture de la page
 */
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});

/**
 * Gestion des erreurs globales
 */
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript dans la messagerie:', e.error);
});

// Export des fonctions pour utilisation globale
window.MessagerieFunctions = {
    rechercherContactsAmeliore,
    clearSearch,
    refreshMessages,
    selectContact,
    updateContactsCount,
    updateConnectionStatus
};

/* ===== FONCTIONNALITÉS AVANCÉES DE MESSAGERIE ===== */

// Variables pour la messagerie avancée
let attachedFiles = [];
let isEmojiPickerOpen = false;
let typingTimer = null;
let lastTypingTime = 0;
let currentRecipientId = null;

// Emojis par catégorie
const emojiCategories = {
    recent: ['😀', '😂', '❤️', '👍', '👎', '😊', '😢', '😡'],
    smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
    people: ['👤', '👥', '👶', '👧', '🧒', '👦', '👩', '🧑', '👨', '👵', '🧓', '👴', '👲', '👳', '👮', '👷', '💂', '🕵️', '👩‍⚕️', '👨‍⚕️', '👩‍🌾', '👨‍🌾'],
    nature: ['🌿', '🌱', '🌳', '🌲', '🌴', '🌵', '🌾', '🌻', '🌺', '🌸', '🌼', '🌷', '🥀', '🌹', '🌪️', '🔥', '💧', '❄️', '⭐', '🌟', '💫', '✨'],
    food: ['🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙', '🧆', '🥚', '🍳', '🥘', '🍲', '🥗', '🍿', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖'],
    activities: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹'],
    travel: ['✈️', '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🛼', '🚁'],
    objects: ['💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴', '💶', '💷', '🪙', '💰', '💳', '💎', '⚖️', '🪜', '🧰', '🔧', '🔨', '⚒️', '🛠️'],
    symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️']
};

/**
 * Gestion améliorée de la saisie de message
 */
function handleMessageInput(textarea) {
    // Auto-resize du textarea
    autoResizeTextarea(textarea);

    // Mise à jour du compteur de caractères
    updateCharacterCounter(textarea);

    // Gestion de l'indicateur de frappe
    handleTypingIndicator();

    // Activation/désactivation du bouton d'envoi
    toggleSendButton();
}

/**
 * Auto-resize du textarea
 */
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    const newHeight = Math.min(textarea.scrollHeight, 120); // Max 120px
    textarea.style.height = newHeight + 'px';
}

/**
 * Mise à jour du compteur de caractères
 */
function updateCharacterCounter(textarea) {
    const charCounter = document.getElementById('charCount');
    const length = textarea.value.length;
    const maxLength = parseInt(textarea.getAttribute('maxlength')) || 5000;

    if (charCounter) {
        charCounter.textContent = `${length}/${maxLength}`;

        // Changer la couleur selon la longueur
        charCounter.className = 'char-counter';
        if (length > maxLength * 0.9) {
            charCounter.classList.add('danger');
        } else if (length > maxLength * 0.8) {
            charCounter.classList.add('warning');
        }
    }
}

/**
 * Gestion de l'indicateur de frappe
 */
function handleTypingIndicator() {
    const now = Date.now();
    lastTypingTime = now;

    // Afficher l'indicateur de frappe
    showTypingIndicator();

    // Masquer après 3 secondes d'inactivité
    clearTimeout(typingTimer);
    typingTimer = setTimeout(() => {
        if (Date.now() - lastTypingTime >= 3000) {
            hideTypingIndicator();
        }
    }, 3000);
}

/**
 * Afficher l'indicateur de frappe
 */
function showTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator && currentRecipientId) {
        indicator.style.display = 'flex';
        // Ici vous pouvez envoyer un signal au serveur pour notifier les autres utilisateurs
        // signalTyping(currentRecipientId, true);
    }
}

/**
 * Masquer l'indicateur de frappe
 */
function hideTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.style.display = 'none';
        // signalTyping(currentRecipientId, false);
    }
}

/**
 * Activer/désactiver le bouton d'envoi
 */
function toggleSendButton() {
    const textarea = document.getElementById('txtMessage');
    const sendButton = document.getElementById('btnenvoie');

    if (textarea && sendButton) {
        const hasText = textarea.value.trim().length > 0;
        const hasAttachments = attachedFiles.length > 0;

        sendButton.disabled = !(hasText || hasAttachments);
    }
}

/**
 * Gestion améliorée de la touche Entrée
 */
function handleEnterKeyImproved(event) {
    if (event.key === 'Enter') {
        if (event.ctrlKey || event.metaKey) {
            // Ctrl+Entrée ou Cmd+Entrée pour envoyer
            event.preventDefault();
            sendMessageImproved();
        } else if (!event.shiftKey) {
            // Entrée simple pour envoyer (sauf si Shift+Entrée pour nouvelle ligne)
            event.preventDefault();
            sendMessageImproved();
        }
        // Shift+Entrée permet une nouvelle ligne (comportement par défaut)
    }
}

/**
 * Envoi amélioré du message
 */
function sendMessageImproved() {
    const textarea = document.getElementById('txtMessage');
    const sendButton = document.getElementById('btnenvoie');
    const messageStatus = document.getElementById('messageStatus');

    if (!textarea || !sendButton || sendButton.disabled) {
        return;
    }

    const messageText = textarea.value.trim();

    if (messageText.length === 0 && attachedFiles.length === 0) {
        return;
    }

    // Désactiver le bouton et changer son état
    sendButton.disabled = true;
    sendButton.classList.add('sending');
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span class="btn-text">Envoi...</span>';

    // Afficher le statut d'envoi
    if (messageStatus) {
        messageStatus.textContent = 'Envoi en cours...';
        messageStatus.className = 'message-status sending';
    }

    // Masquer l'indicateur de frappe
    hideTypingIndicator();

    // Créer l'objet message
    const messageData = {
        text: messageText,
        attachments: attachedFiles.slice(), // Copie du tableau
        timestamp: new Date().toISOString(),
        recipientId: currentRecipientId
    };

    // Affichage optimiste du message
    displayOptimisticMessage(messageData);

    // Vider le champ de saisie immédiatement
    textarea.value = '';
    autoResizeTextarea(textarea);
    updateCharacterCounter(textarea);

    // Vider les pièces jointes
    clearAttachments();

    // Envoi AJAX réel du message
    sendMessageToServer(messageData);
}

/**
 * Affichage optimiste du message
 */
function displayOptimisticMessage(messageData) {
    const messagesContainer = document.querySelector('.chat-messages');
    if (!messagesContainer) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message sent sending new';
    messageDiv.setAttribute('data-temp-id', Date.now());

    let attachmentsHtml = '';
    if (messageData.attachments.length > 0) {
        attachmentsHtml = '<div class="message-attachments">';
        messageData.attachments.forEach(file => {
            attachmentsHtml += `
                <div class="attachment-item">
                    <i class="fas fa-${getFileIcon(file.type)}"></i>
                    <span>${file.name}</span>
                    <small>(${formatFileSize(file.size)})</small>
                </div>
            `;
        });
        attachmentsHtml += '</div>';
    }

    messageDiv.innerHTML = `
        <div class="message-avatar">
            <img src="assets/img/default-avatar.png" alt="Votre avatar">
        </div>
        <div class="message-content">
            <div class="message-bubble">
                ${messageData.text ? `<div class="message-text">${escapeHtml(messageData.text)}</div>` : ''}
                ${attachmentsHtml}
            </div>
            <div class="message-info">
                <span class="message-time">${formatTime(new Date())}</span>
                <i class="fas fa-clock message-status-icon sending" title="Envoi en cours"></i>
            </div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

/**
 * Gestion du message envoyé avec succès
 */
function handleMessageSent(messageData) {
    const sendButton = document.getElementById('btnenvoie');
    const messageStatus = document.getElementById('messageStatus');

    // Restaurer le bouton d'envoi
    if (sendButton) {
        sendButton.classList.remove('sending');
        sendButton.classList.add('sent');
        sendButton.innerHTML = '<i class="fas fa-check"></i><span class="btn-text">Envoyé</span>';

        setTimeout(() => {
            sendButton.classList.remove('sent');
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i><span class="btn-text">Envoyer</span>';
            toggleSendButton();
        }, 2000);
    }

    // Mettre à jour le statut
    if (messageStatus) {
        messageStatus.textContent = 'Message envoyé';
        messageStatus.className = 'message-status sent';

        setTimeout(() => {
            messageStatus.textContent = '';
            messageStatus.className = 'message-status';
        }, 3000);
    }

    // Mettre à jour le message optimiste
    const tempMessage = document.querySelector(`[data-temp-id="${messageData.timestamp}"]`);
    if (tempMessage) {
        tempMessage.classList.remove('sending');
        tempMessage.classList.add('sent');

        const statusIcon = tempMessage.querySelector('.message-status-icon');
        if (statusIcon) {
            statusIcon.className = 'fas fa-check message-status-icon sent';
            statusIcon.title = 'Message envoyé';
        }
    }

    // Ici vous pouvez déclencher un postback pour sauvegarder en base de données
    // __doPostBack('sendMessage', JSON.stringify(messageData));
}

/* ===== GESTION DES PIÈCES JOINTES ===== */

/**
 * Ouvrir le sélecteur de fichiers
 */
function openFileSelector() {
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.click();
    }
}

/**
 * Gestion de la sélection de fichiers
 */
function handleFileSelection(input) {
    const files = Array.from(input.files);

    files.forEach(file => {
        // Validation du fichier
        if (validateFile(file)) {
            attachedFiles.push(file);
        }
    });

    // Vider l'input pour permettre de sélectionner le même fichier à nouveau
    input.value = '';

    // Mettre à jour l'affichage
    updateAttachmentPreview();
    toggleSendButton();
}

/**
 * Validation des fichiers
 */
function validateFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain', 'application/zip', 'application/x-rar-compressed'
    ];

    if (file.size > maxSize) {
        showNotification(`Le fichier "${file.name}" est trop volumineux (max 10MB)`, 'error');
        return false;
    }

    if (!allowedTypes.includes(file.type)) {
        showNotification(`Type de fichier non autorisé: "${file.name}"`, 'error');
        return false;
    }

    // Vérifier si le fichier n'est pas déjà attaché
    if (attachedFiles.some(f => f.name === file.name && f.size === file.size)) {
        showNotification(`Le fichier "${file.name}" est déjà attaché`, 'warning');
        return false;
    }

    return true;
}

/**
 * Mettre à jour la prévisualisation des pièces jointes
 */
function updateAttachmentPreview() {
    const preview = document.getElementById('attachmentPreview');
    const list = document.getElementById('attachmentList');

    if (!preview || !list) return;

    if (attachedFiles.length === 0) {
        preview.style.display = 'none';
        return;
    }

    preview.style.display = 'block';
    list.innerHTML = '';

    attachedFiles.forEach((file, index) => {
        const item = document.createElement('div');
        item.className = 'attachment-item';
        item.innerHTML = `
            <i class="fas fa-${getFileIcon(file.type)}"></i>
            <span class="file-name">${file.name}</span>
            <small class="file-size">(${formatFileSize(file.size)})</small>
            <button type="button" class="attachment-remove" onclick="removeAttachment(${index})" title="Supprimer">
                <i class="fas fa-times"></i>
            </button>
        `;
        list.appendChild(item);
    });
}

/**
 * Supprimer une pièce jointe
 */
function removeAttachment(index) {
    attachedFiles.splice(index, 1);
    updateAttachmentPreview();
    toggleSendButton();
}

/**
 * Vider toutes les pièces jointes
 */
function clearAttachments() {
    attachedFiles = [];
    updateAttachmentPreview();
}

/**
 * Obtenir l'icône appropriée pour un type de fichier
 */
function getFileIcon(fileType) {
    if (fileType.startsWith('image/')) return 'image';
    if (fileType === 'application/pdf') return 'file-pdf';
    if (fileType.includes('word')) return 'file-word';
    if (fileType.includes('excel')) return 'file-excel';
    if (fileType.includes('powerpoint')) return 'file-powerpoint';
    if (fileType === 'text/plain') return 'file-alt';
    if (fileType.includes('zip') || fileType.includes('rar')) return 'file-archive';
    return 'file';
}

/**
 * Formater la taille d'un fichier
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/* ===== GESTION DES EMOJIS ===== */

/**
 * Basculer l'affichage du sélecteur d'emojis
 */
function toggleEmojiPicker() {
    const picker = document.getElementById('emojiPicker');
    if (!picker) return;

    if (isEmojiPickerOpen) {
        closeEmojiPicker();
    } else {
        openEmojiPicker();
    }
}

/**
 * Ouvrir le sélecteur d'emojis
 */
function openEmojiPicker() {
    const picker = document.getElementById('emojiPicker');
    if (!picker) return;

    picker.style.display = 'block';
    isEmojiPickerOpen = true;

    // Charger les emojis de la catégorie active
    loadEmojiCategory('recent');

    // Fermer si on clique ailleurs
    setTimeout(() => {
        document.addEventListener('click', closeEmojiPickerOnOutsideClick);
    }, 100);
}

/**
 * Fermer le sélecteur d'emojis
 */
function closeEmojiPicker() {
    const picker = document.getElementById('emojiPicker');
    if (!picker) return;

    picker.style.display = 'none';
    isEmojiPickerOpen = false;

    document.removeEventListener('click', closeEmojiPickerOnOutsideClick);
}

/**
 * Fermer le sélecteur d'emojis si on clique ailleurs
 */
function closeEmojiPickerOnOutsideClick(event) {
    const picker = document.getElementById('emojiPicker');
    const emojiButton = document.querySelector('.btn-emoji');

    if (picker && !picker.contains(event.target) && !emojiButton.contains(event.target)) {
        closeEmojiPicker();
    }
}

/**
 * Charger les emojis d'une catégorie
 */
function loadEmojiCategory(category) {
    const grid = document.getElementById('emojiGrid');
    const categories = document.querySelectorAll('.emoji-category');

    if (!grid) return;

    // Mettre à jour la catégorie active
    categories.forEach(cat => {
        cat.classList.remove('active');
        if (cat.dataset.category === category) {
            cat.classList.add('active');
        }
    });

    // Charger les emojis
    const emojis = emojiCategories[category] || [];
    grid.innerHTML = '';

    emojis.forEach(emoji => {
        const button = document.createElement('button');
        button.className = 'emoji-item';
        button.textContent = emoji;
        button.title = emoji;
        button.onclick = () => insertEmoji(emoji);
        grid.appendChild(button);
    });
}

/**
 * Insérer un emoji dans le textarea
 */
function insertEmoji(emoji) {
    const textarea = document.getElementById('txtMessage');
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;

    textarea.value = text.substring(0, start) + emoji + text.substring(end);
    textarea.selectionStart = textarea.selectionEnd = start + emoji.length;

    // Déclencher les événements
    textarea.focus();
    handleMessageInput(textarea);

    // Ajouter à la liste des emojis récents
    addToRecentEmojis(emoji);

    // Fermer le sélecteur
    closeEmojiPicker();
}

/**
 * Ajouter un emoji aux récents
 */
function addToRecentEmojis(emoji) {
    let recent = emojiCategories.recent;

    // Supprimer s'il existe déjà
    const index = recent.indexOf(emoji);
    if (index > -1) {
        recent.splice(index, 1);
    }

    // Ajouter au début
    recent.unshift(emoji);

    // Limiter à 8 emojis récents
    if (recent.length > 8) {
        recent = recent.slice(0, 8);
    }

    emojiCategories.recent = recent;
}

/* ===== FONCTIONS UTILITAIRES ===== */

/**
 * Échapper le HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Formater l'heure
 */
function formatTime(date) {
    return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Faire défiler vers le bas
 */
function scrollToBottom() {
    const container = document.querySelector('.chat-messages');
    if (container) {
        container.scrollTop = container.scrollHeight;
    }
}

/**
 * Afficher une notification
 */
function showNotification(message, type = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Ajouter au DOM
    document.body.appendChild(notification);

    // Animation d'entrée
    setTimeout(() => notification.classList.add('show'), 100);

    // Suppression automatique après 5 secondes
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

/**
 * Obtenir l'icône de notification
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

// Initialiser les gestionnaires d'événements pour les emojis
document.addEventListener('DOMContentLoaded', function() {
    // Gestionnaires pour les catégories d'emojis
    const categories = document.querySelectorAll('.emoji-category');
    categories.forEach(category => {
        category.addEventListener('click', function() {
            loadEmojiCategory(this.dataset.category);
        });
    });
});

/* ===== FONCTIONS AJAX POUR MESSAGERIE TEMPS RÉEL ===== */

/**
 * Envoyer le message au serveur via AJAX
 */
function sendMessageToServer(messageData) {
    const attachmentsJson = JSON.stringify(messageData.attachments.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type
    })));

    // Préparer les données pour l'envoi
    const postData = {
        messageText: messageData.text,
        recipientId: (messageData.recipientId || currentRecipientId || '1').toString(), // Valeur par défaut pour les tests
        attachments: attachmentsJson
    };

    // Appel AJAX
    fetch('/messagerie.aspx/SendMessageAjax', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=utf-8',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(postData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.d) {
            const result = JSON.parse(data.d);
            if (result.success) {
                handleMessageSent(messageData, result.data);
            } else {
                handleMessageError(messageData, result.message);
            }
        } else {
            handleMessageError(messageData, 'Réponse serveur invalide');
        }
    })
    .catch(error => {
        console.error('Erreur AJAX:', error);
        handleMessageError(messageData, 'Erreur de connexion');
    });
}

/**
 * Gestion des erreurs d'envoi
 */
function handleMessageError(messageData, errorMessage) {
    const sendButton = document.getElementById('btnenvoie');
    const messageStatus = document.getElementById('messageStatus');

    // Restaurer le bouton d'envoi
    if (sendButton) {
        sendButton.classList.remove('sending');
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i><span class="btn-text">Envoyer</span>';
        toggleSendButton();
    }

    // Afficher l'erreur
    if (messageStatus) {
        messageStatus.textContent = `Erreur: ${errorMessage}`;
        messageStatus.className = 'message-status error';

        setTimeout(() => {
            messageStatus.textContent = '';
            messageStatus.className = 'message-status';
        }, 5000);
    }

    // Marquer le message comme en erreur
    const tempMessage = document.querySelector(`[data-temp-id="${messageData.timestamp}"]`);
    if (tempMessage) {
        tempMessage.classList.remove('sending');
        tempMessage.classList.add('error');

        const statusIcon = tempMessage.querySelector('.message-status-icon');
        if (statusIcon) {
            statusIcon.className = 'fas fa-exclamation-triangle message-status-icon error';
            statusIcon.title = 'Erreur d\'envoi';
        }
    }

    // Afficher une notification
    showNotification(`Erreur d'envoi: ${errorMessage}`, 'error');

    // Remettre le message dans le champ de saisie
    const textarea = document.getElementById('txtMessage');
    if (textarea && messageData.text) {
        textarea.value = messageData.text;
        handleMessageInput(textarea);
    }

    // Remettre les pièces jointes
    if (messageData.attachments && messageData.attachments.length > 0) {
        attachedFiles = messageData.attachments.slice();
        updateAttachmentPreview();
    }
}

/**
 * Récupérer les nouveaux messages
 */
function checkForNewMessages() {
    if (!currentRecipientId) return;

    const lastMessage = document.querySelector('.message:last-child');
    const lastMessageId = lastMessage ? lastMessage.dataset.messageId || '0' : '0';

    fetch('/messagerie.aspx/GetNewMessages', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=utf-8',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            conversationId: currentRecipientId,
            lastMessageId: lastMessageId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.d) {
            const result = JSON.parse(data.d);
            if (result.success && result.data.hasNewMessages) {
                displayNewMessages(result.data.messages);
            }
        }
    })
    .catch(error => {
        console.error('Erreur lors de la vérification des nouveaux messages:', error);
    });
}

/**
 * Afficher les nouveaux messages reçus
 */
function displayNewMessages(messages) {
    const messagesContainer = document.querySelector('.chat-messages');
    if (!messagesContainer) return;

    messages.forEach(message => {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message received new';
        messageDiv.setAttribute('data-message-id', message.id);

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <img src="${message.senderAvatar || 'assets/img/default-avatar.png'}" alt="Avatar">
            </div>
            <div class="message-content">
                <div class="message-bubble">
                    <div class="message-text">${escapeHtml(message.text)}</div>
                </div>
                <div class="message-info">
                    <span class="message-time">${formatTime(new Date(message.timestamp))}</span>
                </div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
    });

    scrollToBottom();

    // Marquer les messages comme lus
    markMessagesAsRead();
}

/**
 * Marquer les messages comme lus
 */
function markMessagesAsRead() {
    if (!currentRecipientId) return;

    fetch('/messagerie.aspx/MarkMessagesAsRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=utf-8',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            conversationId: currentRecipientId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.d) {
            const result = JSON.parse(data.d);
            if (result.success) {
                // Messages marqués comme lus avec succès
                console.log('Messages marqués comme lus');
            }
        }
    })
    .catch(error => {
        console.error('Erreur lors du marquage des messages comme lus:', error);
    });
}

/**
 * Démarrer la vérification périodique des nouveaux messages
 */
function startMessagePolling() {
    // Vérifier les nouveaux messages toutes les 3 secondes
    setInterval(checkForNewMessages, 3000);
}

/**
 * Sélectionner un contact et démarrer la conversation
 */
function selectContactImproved(contactId, contactName) {
    currentRecipientId = contactId;

    // Mettre à jour l'interface
    const contacts = document.querySelectorAll('.contact-item');
    contacts.forEach(contact => {
        contact.classList.remove('active');
    });

    const selectedContact = document.querySelector(`[data-contact-id="${contactId}"]`);
    if (selectedContact) {
        selectedContact.classList.add('active');
    }

    // Mettre à jour l'en-tête de la conversation
    const chatHeader = document.querySelector('.chat-header');
    if (chatHeader) {
        chatHeader.innerHTML = `
            <div class="chat-header-info">
                <h3>${escapeHtml(contactName)}</h3>
                <span class="contact-status">En ligne</span>
            </div>
            <div class="chat-header-actions">
                <button onclick="refreshMessages()" title="Actualiser">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        `;
    }

    // Marquer les messages comme lus
    markMessagesAsRead();

    console.log(`Contact sélectionné: ${contactName} (ID: ${contactId})`);
}

// Démarrer la vérification des messages au chargement
document.addEventListener('DOMContentLoaded', function() {
    startMessagePolling();
});
