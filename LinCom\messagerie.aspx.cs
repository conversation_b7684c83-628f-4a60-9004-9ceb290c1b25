﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;

        // Nouvelles propriétés pour la pagination et la sécurité
        private const int MESSAGES_PER_PAGE = 20;
        private const int MAX_MESSAGE_LENGTH = 5000;
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

            }
            if (!IsPostBack)
            {
                // Par défaut, tu peux initialiser avec une conversation
                
                ChargerMessages();
                AppelMethode();
            }
        }
        public void AppelMethode()
        {
            objmem.ChargerListview(listmembre,-1,"actif","");

          
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewmem")
            {
                long idMembre =Convert.ToInt64( e.CommandArgument.ToString());
                // Utilisez l'ID pour récupérer les détails du membre
                objmem.AfficherDetails(idMembre,mem);
               
                // Changez le titre de la discussion
                lblHeader.Text = "Message envoyé à " + mem.Nom+" "+mem.Prenom;
                lblId.Text = mem.MembreId.ToString();

                ChargerMessages();

            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private int CreationMessage(long convID,long membrId)
        {
            mess.ConversationId = convID;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Value;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";
            mess.AttachmentUrl = "";
            info=objmes.Envoyer(mess);

            return info;

        }
        private int CreationMessagestatus(long convID, long membrId,int lire)
        {
            messtatu.MessageId = convID;
            messtatu.UserId = membrId;
            messtatu.IsRead = lire;
            messtatu.ReadAt = DateTime.Now;
           
            info =objmes.EnvoyerMessageStatus(messtatu);

            return info;
        }

        private void EnvoieMessagerie()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info=0,info1 = 0,info2=0;

                if (isGroup)
                {//il faut continuer l'implementation
                    // Groupe : conversation déjà existante via id du groupe
                    long idGroupe = destinataireId;

                    // Ici, on ajoute le message à la table des messages de groupe
                  ///  info = objmes.AjouterMessageDansGroupe(idGroupe, senderId, txtMessage.Value);
                  //  if (info == 1)
                   //     objmes.ChargerMessagesGroupe(rptMessages, idGroupe, 50);
                }
                else
                {
                    CreationParticipantConversation(destinataireId);

                    // Tchat privé
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    //if (conversationId <= 0)
                    //    CreationConversation(0, "");

                   
                   // conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    info=CreationMessage(conversationId,senderId);
                    info1=CreationMessagestatus(conversationId,senderId,1);
                    info2=CreationMessagestatus(conversationId,destinataireId,0);

                    if (info == 1 && info1==1 && info2==1)
                        ChargerMessages();
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
            }
        }
        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            EnvoieMessagerie();
        }
        void EnvoieMessage()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long conversationreceveurmembre = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));
               
                if (conversationreceveurmembre <= 0)
                    CreationConversation(0,"");

                long conversationreceveurmembreencore = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));

                mess.ConversationId = conversationreceveurmembreencore;
                mess.SenderId = ide;
                mess.Contenu = txtMessage.Value.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = null;

               info= objmes.Envoyer(mess);

                if (info==1)
                {
                    //CreationParticipantConversation();

                 //   Response.Write("<script LANGUAGE=JavaScript>alert('Message envoyé')</script>");

                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Erreur')</script>");


                }
            }
        }
        void ChargerMessages()
        {
            //chargement des messages avec pagination améliorée
            long senderId = ide;
            long destinataireId = Convert.ToInt64(lblId.Text);
            long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

            // Utiliser la pagination avec une limite raisonnable
            objmes.ChargerMessages(rptMessages, conversationId, MESSAGES_PER_PAGE, 1);
        }

        // Nouvelle méthode améliorée pour l'envoi de messages
        private void EnvoieMessagerieAmeliore()
        {
            try
            {
                // Vérification du rate limiting
                if (!SecurityHelper.CheckRateLimit(ide, 30, 1)) // 30 messages par minute max
                {
                    Response.Write("<script>alert('Trop de messages envoyés. Veuillez patienter.');</script>");
                    return;
                }

                // Validation et sanitisation du contenu
                string contenu = txtMessage.Value?.Trim();
                if (!SecurityHelper.ValidateMessage(contenu, MAX_MESSAGE_LENGTH))
                {
                    Response.Write("<script>alert('Message invalide. Vérifiez le contenu et la longueur.');</script>");
                    return;
                }

                // Sanitiser le contenu
                contenu = SecurityHelper.SanitizeHtml(contenu);

                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";

                // Vérifier les autorisations
                if (!SecurityHelper.ValidateId(senderId) || !SecurityHelper.ValidateId(destinataireId))
                {
                    Response.Write("<script>alert('Erreur de validation des identifiants.');</script>");
                    return;
                }

                int info = 0, info1 = 0, info2 = 0;

                if (isGroup)
                {
                    // Groupe : conversation déjà existante via id du groupe
                    long idGroupe = destinataireId;
                    // TODO: Implémenter la logique des groupes
                }
                else
                {
                    // Vérifier que l'utilisateur a le droit d'envoyer un message à ce destinataire
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    if (conversationId > 0 && !objmes.VerifierAutorisationConversation(senderId, conversationId))
                    {
                        Response.Write("<script>alert('Vous n\\'avez pas l\\'autorisation d\\'écrire dans cette conversation.');</script>");
                        return;
                    }

                    CreationParticipantConversation(destinataireId);
                    conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    // Mettre à jour le contenu sanitisé
                    txtMessage.Value = contenu;

                    info = CreationMessage(conversationId, senderId);
                    info1 = CreationMessagestatus(conversationId, senderId, 1);
                    info2 = CreationMessagestatus(conversationId, destinataireId, 0);

                    if (info == 1 && info1 == 1 && info2 == 1)
                    {
                        ChargerMessages();
                        txtMessage.Value = ""; // Vider le champ après envoi réussi
                    }
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l\\'envoi du message.');</script>");
            }
            catch (Exception ex)
            {
                // Log de l'erreur
                System.Diagnostics.Debug.WriteLine($"Erreur dans EnvoieMessagerie: {ex.Message}");
                Response.Write("<script>alert('Une erreur inattendue s\\'est produite.');</script>");
            }
        }

        // Méthode pour rechercher des contacts
        protected void RechercherContacts(string termeRecherche)
        {
            try
            {
                // Nettoyer le terme de recherche
                termeRecherche = SecurityHelper.CleanSearchTerm(termeRecherche);

                if (SecurityHelper.ContainsSqlInjection(termeRecherche))
                {
                    Response.Write("<script>alert('Terme de recherche invalide.');</script>");
                    return;
                }

                // Utiliser la nouvelle méthode de recherche
                objconver.RechercherMembres(listmembre, ide, termeRecherche);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur dans RechercherContacts: {ex.Message}");
                Response.Write("<script>alert('Erreur lors de la recherche.');</script>");
            }
        }

        // Méthode pour charger plus de messages (pagination)
        protected void ChargerPlusDeMessages(int pageNumber)
        {
            try
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                if (conversationId > 0)
                {
                    objmes.ChargerMessagesAvecPagination(rptMessages, conversationId, MESSAGES_PER_PAGE, pageNumber);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur dans ChargerPlusDeMessages: {ex.Message}");
            }
        }

        // ⚠️ À remplacer avec le vrai utilisateur connecté

        #region Méthodes Utilitaires Améliorées

        /// <summary>
        /// Obtenir l'URL sécurisée de l'avatar
        /// </summary>
        /// <param name="photoProfile">Nom du fichier photo</param>
        /// <returns>URL sécurisée de l'avatar</returns>
        protected string GetSecureAvatarUrl(object photoProfile)
        {
            try
            {
                if (photoProfile == null || string.IsNullOrWhiteSpace(photoProfile.ToString()))
                {
                    return "assets/img/default-avatar.png";
                }

                string fileName = HttpUtility.HtmlEncode(photoProfile.ToString());
                return $"../file/membr/{fileName}";
            }
            catch (Exception ex)
            {
                LogError("GetSecureAvatarUrl", ex);
                return "assets/img/default-avatar.png";
            }
        }

        /// <summary>
        /// Formater la dernière connexion
        /// </summary>
        /// <param name="lastSeen">Date de dernière connexion</param>
        /// <returns>Texte formaté</returns>
        protected string GetFormattedLastSeen(object lastSeen)
        {
            try
            {
                if (lastSeen == null || lastSeen == DBNull.Value)
                {
                    return "Jamais vu";
                }

                if (DateTime.TryParse(lastSeen.ToString(), out DateTime lastSeenDate))
                {
                    TimeSpan timeDiff = DateTime.Now - lastSeenDate;

                    if (timeDiff.TotalMinutes < 1)
                        return "À l'instant";
                    else if (timeDiff.TotalMinutes < 60)
                        return $"Il y a {(int)timeDiff.TotalMinutes} min";
                    else if (timeDiff.TotalHours < 24)
                        return $"Il y a {(int)timeDiff.TotalHours}h";
                    else if (timeDiff.TotalDays < 7)
                        return $"Il y a {(int)timeDiff.TotalDays}j";
                    else
                        return lastSeenDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
                }

                return "Inconnu";
            }
            catch (Exception ex)
            {
                LogError("GetFormattedLastSeen", ex);
                return "Inconnu";
            }
        }

        /// <summary>
        /// Obtenir l'ID de l'utilisateur connecté
        /// </summary>
        /// <returns>ID de l'utilisateur</returns>
        private long GetCurrentUserId()
        {
            try
            {
                if (Request.Cookies["iduser"] != null)
                {
                    if (long.TryParse(Request.Cookies["iduser"].Value, out long userId))
                    {
                        return userId;
                    }
                }
                return 0;
            }
            catch (Exception ex)
            {
                LogError("GetCurrentUserId", ex);
                return 0;
            }
        }

        /// <summary>
        /// Obtenir le nombre de contacts
        /// </summary>
        /// <returns>Nombre de contacts</returns>
        private int GetContactsCount()
        {
            try
            {
                if (listmembre?.Items != null)
                {
                    return listmembre.Items.Count;
                }
                return 0;
            }
            catch (Exception ex)
            {
                LogError("GetContactsCount", ex);
                return 0;
            }
        }

        /// <summary>
        /// Mettre à jour le compteur de contacts
        /// </summary>
        private void UpdateContactsCount()
        {
            try
            {
                int count = GetContactsCount();

                // Enregistrer un script pour mettre à jour le compteur côté client
                string script = $@"
                    document.addEventListener('DOMContentLoaded', function() {{
                        const countElement = document.getElementById('contactsCount');
                        if (countElement) {{
                            countElement.textContent = '{count}';
                            countElement.title = '{count} contact{(count > 1 ? "s" : "")} disponible{(count > 1 ? "s" : "")}';
                        }}
                    }});
                ";

                ClientScript.RegisterStartupScript(this.GetType(), "UpdateContactsCount", script, true);
            }
            catch (Exception ex)
            {
                LogError("UpdateContactsCount", ex);
            }
        }

        /// <summary>
        /// Afficher une notification à l'utilisateur
        /// </summary>
        /// <param name="message">Message à afficher</param>
        /// <param name="type">Type de notification (success, error, warning, info)</param>
        protected void ShowNotification(string message, string type = "info")
        {
            try
            {
                string safeMessage = HttpUtility.JavaScriptStringEncode(message);
                string script = $@"
                    document.addEventListener('DOMContentLoaded', function() {{
                        showNotification('{safeMessage}', '{type}');
                    }});
                ";

                ClientScript.RegisterStartupScript(this.GetType(), "ShowNotification", script, true);
            }
            catch (Exception ex)
            {
                LogError("ShowNotification", ex);
            }
        }

        /// <summary>
        /// Logger les erreurs de manière sécurisée
        /// </summary>
        /// <param name="methodName">Nom de la méthode</param>
        /// <param name="ex">Exception</param>
        private void LogError(string methodName, Exception ex)
        {
            try
            {
                string errorMessage = $"[Messagerie] {methodName}: {ex.Message}";
                System.Diagnostics.Debug.WriteLine(errorMessage);

                // Ici vous pouvez ajouter d'autres systèmes de logging
                // comme écrire dans un fichier, base de données, etc.
            }
            catch
            {
                // Éviter les erreurs en cascade lors du logging
            }
        }

        /// <summary>
        /// Valider et nettoyer une chaîne de caractères
        /// </summary>
        /// <param name="input">Chaîne à valider</param>
        /// <param name="maxLength">Longueur maximale</param>
        /// <returns>Chaîne nettoyée</returns>
        protected string ValidateAndCleanString(string input, int maxLength = 255)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(input))
                {
                    return string.Empty;
                }

                // Nettoyer et limiter la longueur
                string cleaned = input.Trim();
                if (cleaned.Length > maxLength)
                {
                    cleaned = cleaned.Substring(0, maxLength);
                }

                return HttpUtility.HtmlEncode(cleaned);
            }
            catch (Exception ex)
            {
                LogError("ValidateAndCleanString", ex);
                return string.Empty;
            }
        }

        #endregion

        #region Méthodes AJAX pour Messagerie Temps Réel

        /// <summary>
        /// Envoyer un message via AJAX
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string SendMessageAjax(string messageText, string recipientId, string attachments)
        {
            try
            {
                // Validation des paramètres
                if (string.IsNullOrWhiteSpace(messageText) && string.IsNullOrWhiteSpace(attachments))
                {
                    return CreateJsonResponse(false, "Message vide");
                }

                if (string.IsNullOrWhiteSpace(recipientId) || !long.TryParse(recipientId, out long recipientIdLong))
                {
                    return CreateJsonResponse(false, "Destinataire invalide");
                }

                // Obtenir l'utilisateur connecté (à adapter selon votre système d'authentification)
                long senderId = GetCurrentUserIdStatic();
                if (senderId == 0)
                {
                    return CreateJsonResponse(false, "Utilisateur non connecté");
                }

                // Nettoyer et valider le message
                string cleanMessage = System.Web.HttpUtility.HtmlEncode(messageText?.Trim());
                if (cleanMessage.Length > 5000)
                {
                    return CreateJsonResponse(false, "Message trop long (max 5000 caractères)");
                }

                // Créer le message
                var messageImp = new MessageImp();
                var messageClass = new Message_Class
                {
                    Contenu = cleanMessage,
                    DateEnvoi = DateTime.Now,
                    Expediteur = senderId.ToString(),
                    Destinataire = recipientIdLong.ToString(),
                    StatutMessage = "Envoyé"
                };

                // Sauvegarder le message
                int messageId = messageImp.Ajouter(messageClass);

                if (messageId > 0)
                {
                    // Traiter les pièces jointes si présentes
                    if (!string.IsNullOrWhiteSpace(attachments))
                    {
                        ProcessAttachments(messageId, attachments);
                    }

                    return CreateJsonResponse(true, "Message envoyé", new
                    {
                        messageId = messageId,
                        timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        status = "sent"
                    });
                }
                else
                {
                    return CreateJsonResponse(false, "Erreur lors de l'envoi du message");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur SendMessageAjax: {ex.Message}");
                return CreateJsonResponse(false, "Erreur serveur lors de l'envoi");
            }
        }

        /// <summary>
        /// Récupérer les nouveaux messages via AJAX
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string GetNewMessages(string conversationId, string lastMessageId)
        {
            try
            {
                if (!long.TryParse(conversationId, out long convId) || !long.TryParse(lastMessageId, out long lastMsgId))
                {
                    return CreateJsonResponse(false, "Paramètres invalides");
                }

                var messageImp = new MessageImp();
                // Ici vous devriez implémenter une méthode pour récupérer les nouveaux messages
                // var newMessages = messageImp.GetNewMessages(convId, lastMsgId);

                // Pour l'instant, retourner une réponse vide
                return CreateJsonResponse(true, "Aucun nouveau message", new
                {
                    messages = new object[0],
                    hasNewMessages = false
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur GetNewMessages: {ex.Message}");
                return CreateJsonResponse(false, "Erreur lors de la récupération des messages");
            }
        }

        /// <summary>
        /// Marquer les messages comme lus
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string MarkMessagesAsRead(string conversationId)
        {
            try
            {
                if (!long.TryParse(conversationId, out long convId))
                {
                    return CreateJsonResponse(false, "ID de conversation invalide");
                }

                long currentUserId = GetCurrentUserIdStatic();
                if (currentUserId == 0)
                {
                    return CreateJsonResponse(false, "Utilisateur non connecté");
                }

                // Ici vous devriez implémenter la logique pour marquer les messages comme lus
                // var messageImp = new MessageImp();
                // messageImp.MarkAsRead(convId, currentUserId);

                return CreateJsonResponse(true, "Messages marqués comme lus");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur MarkMessagesAsRead: {ex.Message}");
                return CreateJsonResponse(false, "Erreur lors du marquage des messages");
            }
        }

        /// <summary>
        /// Traiter les pièces jointes
        /// </summary>
        private static void ProcessAttachments(int messageId, string attachmentsJson)
        {
            try
            {
                // Ici vous pouvez implémenter la logique pour traiter les pièces jointes
                // Par exemple, sauvegarder les informations des fichiers en base de données
                System.Diagnostics.Debug.WriteLine($"Traitement des pièces jointes pour le message {messageId}: {attachmentsJson}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur ProcessAttachments: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtenir l'ID de l'utilisateur connecté (méthode statique)
        /// </summary>
        private static long GetCurrentUserIdStatic()
        {
            try
            {
                var context = System.Web.HttpContext.Current;
                if (context?.Request.Cookies["iduser"] != null)
                {
                    if (long.TryParse(context.Request.Cookies["iduser"].Value, out long userId))
                    {
                        return userId;
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Créer une réponse JSON standardisée
        /// </summary>
        private static string CreateJsonResponse(bool success, string message, object data = null)
        {
            var response = new
            {
                success = success,
                message = message,
                data = data,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            return Newtonsoft.Json.JsonConvert.SerializeObject(response);
        }

        #endregion
    }
}