﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMessage
    {
        void AfficherDetails(long messageId, Message_Class message);
        int Envoyer(Message_Class message);
        int Modifier(Message_Class message);
        int Supprimer(long messageId);
        void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages = 50, int page = 1);
        void ChargerMessages(ListView lv, long conversationId, int nombreMessages = 50, int page = 1);
        int CompterNonLus(long membreId);

        //Interface pour Message Statut
        void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass);
        int MarquerCommeLu(long messageId, long userId);
        int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null);
        int SupprimerMessageStatut(long messageStatusId);
        int EnvoyerMessageStatus(MessageStatus_Class messageClass);

        // Nouvelles méthodes pour les améliorations
        bool ValiderMessage(string contenu, long senderId, long conversationId);
        string SanitiserContenu(string contenu);
        bool VerifierAutorisationConversation(long membreId, long conversationId);
        int CompterMessagesConversation(long conversationId);
        void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int pageSize, int pageNumber);
        void ChargerMessagesAvecPagination(ListView lv, long conversationId, int pageSize, int pageNumber);

        // Méthodes génériques pour tous types de contrôles
        void ChargerMessagesGenerique(object control, long conversationId, int pageSize, int pageNumber);
    }
}
