/* ===== MESSAGERIE AMÉLIORÉE - STYLES CSS ===== */

/* Variables CSS pour cohérence et maintenance */
:root {
    --primary-color: #008374;
    --primary-hover: #006b5e;
    --primary-light: #e8f5f3;
    --secondary-color: #f8f9fa;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --border-color: #ddd;
    --border-light: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 8px;
    --border-radius-small: 4px;
    --border-radius-large: 12px;
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Reset et optimisations de base */
.messagerie-container {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
}

/* En-tête de la messagerie */
.messagerie-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: 1.5rem;
}

.messagerie-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-refresh {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-refresh:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--success-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.connection-status i {
    font-size: 0.7rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Wrapper du chat amélioré */
.chat-wrapper {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 1.5rem;
    height: 70vh;
    min-height: 500px;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

/* Panel des contacts amélioré */
.contacts-panel {
    background: var(--secondary-color);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.contacts-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-light);
}

.contacts-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contacts-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-large);
    font-size: 0.8rem;
    font-weight: 600;
}

/* Recherche améliorée */
.contacts-search {
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--text-muted);
    z-index: 1;
}

.contacts-search input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-large);
    font-size: 0.9rem;
    transition: var(--transition-fast);
    background: white;
}

.contacts-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 131, 116, 0.1);
}

.clear-search {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.clear-search:hover {
    background: var(--border-light);
    color: var(--text-primary);
}

.search-results-info {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Liste des contacts */
.contacts-list {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.contacts-list::-webkit-scrollbar {
    width: 6px;
}

.contacts-list::-webkit-scrollbar-track {
    background: transparent;
}

.contacts-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    text-decoration: none;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    transition: var(--transition-fast);
    cursor: pointer;
    gap: 0.75rem;
}

.contact-item:hover {
    background: white;
    text-decoration: none;
    color: var(--text-primary);
    transform: translateX(2px);
}

.contact-item.active {
    background: var(--primary-light);
    border-left: 4px solid var(--primary-color);
}

.contact-avatar {
    position: relative;
    flex-shrink: 0;
}

.contact-avatar img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-light);
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: var(--text-muted);
}

.status-indicator[data-status="online"] {
    background: var(--success-color);
}

.status-indicator[data-status="away"] {
    background: var(--warning-color);
}

.status-indicator[data-status="busy"] {
    background: var(--accent-color);
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-status {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.contact-preview {
    font-size: 0.8rem;
    color: var(--text-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.last-seen {
    font-size: 0.7rem;
    color: var(--text-muted);
    white-space: nowrap;
}

.unread-count {
    background: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: var(--border-radius-large);
    min-width: 18px;
    text-align: center;
}

/* Empty state */
.empty-contacts {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: var(--text-muted);
    height: 200px;
}

.empty-contacts i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-contacts h4 {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.empty-contacts p {
    font-size: 0.9rem;
    max-width: 250px;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-wrapper {
        grid-template-columns: 1fr;
        height: auto;
        min-height: 400px;
    }
    
    .contacts-panel {
        max-height: 300px;
    }
    
    .messagerie-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .contacts-search {
        padding: 0.75rem;
    }

    .contact-item {
        padding: 0.75rem;
    }

    .contact-avatar img {
        width: 40px;
        height: 40px;
    }

    .messagerie-header h1 {
        font-size: 1.5rem;
    }
}

/* ===== ZONE DE SAISIE AMÉLIORÉE ===== */

/* Container principal de la zone de saisie */
.chat-footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-light);
    padding: 1rem;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

/* Prévisualisation des pièces jointes */
.attachment-preview {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
}

.attachment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: white;
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.attachment-item i {
    color: var(--primary-color);
}

.attachment-remove {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.attachment-remove:hover {
    background: var(--accent-color);
    color: white;
}

/* Container de saisie principal */
.message-input-container {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background: white;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-large);
    padding: 0.75rem;
    transition: var(--transition-fast);
    position: relative;
}

.message-input-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 131, 116, 0.1);
}

/* Boutons d'action */
.btn-attachment,
.btn-emoji {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.btn-attachment:hover,
.btn-emoji:hover {
    background: var(--primary-light);
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Wrapper du textarea */
.textarea-wrapper {
    flex: 1;
    position: relative;
    min-height: 40px;
}

.textarea-wrapper textarea {
    width: 100%;
    border: none;
    background: transparent;
    resize: none;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.4;
    padding: 0.5rem 0;
    max-height: 120px;
    min-height: 40px;
    color: var(--text-primary);
    outline: none;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.textarea-wrapper textarea::-webkit-scrollbar {
    width: 4px;
}

.textarea-wrapper textarea::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

.textarea-wrapper textarea::placeholder {
    color: var(--text-muted);
}

/* Indicateur de frappe */
.typing-indicator {
    position: absolute;
    bottom: -25px;
    left: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-muted);
    animation: fadeIn 0.3s ease;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Bouton d'envoi amélioré */
.btn-send {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #ccc;
    color: white;
    border: none;
    border-radius: var(--border-radius-large);
    cursor: not-allowed;
    font-weight: 600;
    transition: var(--transition-fast);
    flex-shrink: 0;
    min-width: 100px;
    justify-content: center;
    opacity: 0.6;
}

/* État prêt à envoyer */
.btn-send.ready,
.btn-send:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    cursor: pointer;
    opacity: 1;
}

.btn-send.ready:hover,
.btn-send:not(:disabled):hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

/* État désactivé */
.btn-send:disabled,
.btn-send.disabled {
    background: #ccc !important;
    cursor: not-allowed !important;
    transform: none !important;
    opacity: 0.6 !important;
    box-shadow: none !important;
}

/* État envoi en cours */
.btn-send.sending {
    background: var(--warning-color) !important;
    pointer-events: none;
    cursor: wait !important;
    opacity: 1 !important;
}

/* État envoyé */
.btn-send.sent {
    background: var(--success-color) !important;
    animation: pulse 0.5s ease;
    opacity: 1 !important;
}

/* Animation pulse pour le succès */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.btn-send .btn-text {
    font-size: 0.9rem;
}

/* Informations du footer */
.message-footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.char-counter {
    color: var(--text-muted);
    transition: var(--transition-fast);
}

.char-counter.warning {
    color: var(--warning-color);
}

.char-counter.danger {
    color: var(--accent-color);
    font-weight: 600;
}

.message-status {
    margin-left: 1rem;
    color: var(--text-secondary);
    font-style: italic;
}

.message-status.sending {
    color: var(--warning-color);
}

.message-status.sent {
    color: var(--success-color);
}

.message-status.error {
    color: var(--accent-color);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--success-color);
}

.connection-status.offline {
    color: var(--accent-color);
}

.connection-status i {
    font-size: 0.6rem;
    animation: pulse 2s infinite;
}

/* ===== SÉLECTEUR D'EMOJIS ===== */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    right: 0;
    width: 320px;
    height: 300px;
    background: white;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    z-index: 1000;
    animation: slideUpFadeIn 0.3s ease;
    overflow: hidden;
}

@keyframes slideUpFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.emoji-categories {
    display: flex;
    background: var(--secondary-color);
    border-bottom: 1px solid var(--border-light);
    padding: 0.5rem;
    gap: 0.25rem;
    overflow-x: auto;
}

.emoji-category {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    font-size: 1.2rem;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.emoji-category:hover,
.emoji-category.active {
    background: var(--primary-light);
}

.emoji-grid {
    padding: 0.5rem;
    height: calc(100% - 60px);
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.25rem;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.emoji-grid::-webkit-scrollbar {
    width: 6px;
}

.emoji-grid::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.emoji-item {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    font-size: 1.2rem;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-item:hover {
    background: var(--primary-light);
    transform: scale(1.2);
}

/* ===== ANIMATIONS DE MESSAGES ===== */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes messageSent {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.message.new {
    animation: messageSlideIn 0.3s ease;
}

.message.sending {
    opacity: 0.7;
    transform: scale(0.98);
}

.message.sent {
    animation: messageSent 0.5s ease;
}

/* ===== RESPONSIVE POUR LA ZONE DE SAISIE ===== */
@media (max-width: 768px) {
    .message-input-container {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .btn-attachment,
    .btn-emoji {
        width: 36px;
        height: 36px;
    }

    .btn-send {
        min-width: 80px;
        padding: 0.5rem 0.75rem;
    }

    .btn-send .btn-text {
        display: none;
    }

    .emoji-picker {
        width: 280px;
        height: 250px;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (max-width: 480px) {
    .chat-footer {
        padding: 0.75rem;
    }

    .message-input-container {
        flex-wrap: wrap;
    }

    .textarea-wrapper {
        order: 1;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .btn-attachment {
        order: 2;
    }

    .btn-emoji {
        order: 3;
    }

    .btn-send {
        order: 4;
        margin-left: auto;
    }

    .emoji-picker {
        width: calc(100vw - 2rem);
        right: -0.75rem;
    }

    .attachment-preview {
        margin-bottom: 0.5rem;
        padding: 0.5rem;
    }
}

/* ===== NOTIFICATIONS ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    border-left: 4px solid var(--info-color);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.notification.error {
    border-left-color: var(--accent-color);
    color: var(--accent-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
    color: var(--warning-color);
}

.notification button {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition-fast);
    margin-left: auto;
}

.notification button:hover {
    background: var(--secondary-color);
    color: var(--text-primary);
}

/* ===== MESSAGES AMÉLIORÉS ===== */
.chat-messages, .chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
}

.message {
    display: flex;
    gap: 0.75rem;
    max-width: 70%;
    animation: messageSlideIn 0.3s ease;
    margin-bottom: 0.5rem;
}

.message.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message.sent .message-bubble {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-radius: 18px 18px 4px 18px;
}

.message.received .message-bubble {
    background: var(--secondary-color);
    color: var(--text-primary);
    border-radius: 18px 18px 18px 4px;
}

.message.new {
    animation: messageSlideIn 0.3s ease;
}

.message-avatar {
    flex-shrink: 0;
}

.message-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-bubble {
    padding: 0.75rem 1rem;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
    max-width: 100%;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-text {
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
}

.message-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.message.sent .message-info {
    justify-content: flex-end;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
}

.message-status-icon {
    font-size: 0.7rem;
}

.message-status-icon.sending {
    color: var(--warning-color);
    animation: pulse 1s infinite;
}

.message-status-icon.sent {
    color: var(--success-color);
}

.message-status-icon.delivered {
    color: var(--info-color);
}

.message-status-icon.read {
    color: var(--primary-color);
}

.message-attachments {
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.message-attachments .attachment-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: inherit;
}

.message-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.message.sent .message-info {
    justify-content: flex-end;
}

.message-status-icon {
    font-size: 0.7rem;
}

.message-status-icon.sending {
    color: var(--warning-color);
    animation: pulse 1s infinite;
}

.message-status-icon.sent {
    color: var(--success-color);
}

.message-status-icon.delivered {
    color: var(--info-color);
}

.message-status-icon.read {
    color: var(--primary-color);
}

/* ===== VARIABLES CSS SUPPLÉMENTAIRES ===== */
:root {
    --font-size-base: 0.95rem;
    --bg-primary: #ffffff;
}

/* ===== CONTACT SÉLECTIONNÉ ===== */
.contact-item.active {
    background: var(--primary-light) !important;
    border-left: 4px solid var(--primary-color);
    font-weight: 600;
}

.contact-item.active .contact-name {
    color: var(--primary-color);
}

/* ===== NOTIFICATIONS NOUVEAUX MESSAGES ===== */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

.new-message-notification {
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== MESSAGES REÇUS ===== */
.message.received {
    align-self: flex-start;
}

.message.received .message-bubble {
    background: var(--secondary-color);
    color: var(--text-primary);
    border-radius: 18px 18px 18px 4px;
}

.message.received .message-info {
    justify-content: flex-start;
}

.message-sender {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* ===== CONTACT SÉLECTIONNÉ ===== */
.contact-item.active {
    background: var(--primary-light) !important;
    border-left: 4px solid var(--primary-color);
    font-weight: 600;
}

.contact-item.active .contact-name {
    color: var(--primary-color);
}

/* ===== NOTIFICATIONS NOUVEAUX MESSAGES ===== */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

.new-message-notification {
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== MESSAGES REÇUS ===== */
.message.received {
    align-self: flex-start;
}

.message.received .message-bubble {
    background: var(--secondary-color);
    color: var(--text-primary);
    border-radius: 18px 18px 18px 4px;
}

.message.received .message-info {
    justify-content: flex-start;
}

.message-sender {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* ===== ÉTATS DE CONNEXION ===== */
.contact-status {
    font-size: 0.8rem;
    color: var(--success-color);
}

.contact-status.offline {
    color: var(--text-muted);
}

/* ===== AMÉLIORATIONS RESPONSIVE ===== */
@media (max-width: 768px) {
    .new-message-notification {
        right: 10px;
        left: 10px;
        text-align: center;
    }

    .contact-item.active {
        border-left: none;
        border-top: 3px solid var(--primary-color);
    }
}
