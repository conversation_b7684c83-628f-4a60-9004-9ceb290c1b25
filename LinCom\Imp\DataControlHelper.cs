using System;
using System.Collections.Generic;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    /// <summary>
    /// Classe utilitaire pour gérer les contrôles de données (Repeater, ListView, etc.)
    /// </summary>
    public static class DataControlHelper
    {
        /// <summary>
        /// Lie des données à un contrôle (Repeater ou ListView)
        /// </summary>
        /// <param name="control">Le contrôle à lier</param>
        /// <param name="dataSource">La source de données</param>
        public static void BindData(object control, object dataSource)
        {
            if (control == null || dataSource == null)
                return;

            try
            {
                if (control is Repeater repeater)
                {
                    repeater.DataSource = dataSource;
                    repeater.DataBind();
                }
                else if (control is ListView listView)
                {
                    listView.DataSource = dataSource;
                    listView.DataBind();
                }
                else if (control is GridView gridView)
                {
                    gridView.DataSource = dataSource;
                    gridView.DataBind();
                }
                else if (control is DataList dataList)
                {
                    dataList.DataSource = dataSource;
                    dataList.DataBind();
                }
                else
                {
                    throw new ArgumentException($"Type de contrôle non supporté: {control.GetType().Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la liaison de données: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Vérifie si un contrôle est un contrôle de données supporté
        /// </summary>
        /// <param name="control">Le contrôle à vérifier</param>
        /// <returns>True si le contrôle est supporté</returns>
        public static bool IsSupportedDataControl(object control)
        {
            return control is Repeater || 
                   control is ListView || 
                   control is GridView || 
                   control is DataList;
        }

        /// <summary>
        /// Obtient le nom du type de contrôle
        /// </summary>
        /// <param name="control">Le contrôle</param>
        /// <returns>Le nom du type</returns>
        public static string GetControlTypeName(object control)
        {
            return control?.GetType().Name ?? "Unknown";
        }

        /// <summary>
        /// Vide un contrôle de données
        /// </summary>
        /// <param name="control">Le contrôle à vider</param>
        public static void ClearData(object control)
        {
            if (control == null)
                return;

            try
            {
                if (control is Repeater repeater)
                {
                    repeater.DataSource = null;
                    repeater.DataBind();
                }
                else if (control is ListView listView)
                {
                    listView.DataSource = null;
                    listView.DataBind();
                }
                else if (control is GridView gridView)
                {
                    gridView.DataSource = null;
                    gridView.DataBind();
                }
                else if (control is DataList dataList)
                {
                    dataList.DataSource = null;
                    dataList.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du vidage des données: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtient le nombre d'éléments dans un contrôle de données
        /// </summary>
        /// <param name="control">Le contrôle</param>
        /// <returns>Le nombre d'éléments</returns>
        public static int GetItemCount(object control)
        {
            if (control == null)
                return 0;

            try
            {
                if (control is Repeater repeater)
                {
                    return repeater.Items.Count;
                }
                else if (control is ListView listView)
                {
                    return listView.Items.Count;
                }
                else if (control is GridView gridView)
                {
                    return gridView.Rows.Count;
                }
                else if (control is DataList dataList)
                {
                    return dataList.Items.Count;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du comptage des éléments: {ex.Message}");
            }

            return 0;
        }

        /// <summary>
        /// Applique un style CSS à un contrôle
        /// </summary>
        /// <param name="control">Le contrôle</param>
        /// <param name="cssClass">La classe CSS</param>
        public static void ApplyCssClass(object control, string cssClass)
        {
            if (control == null || string.IsNullOrEmpty(cssClass))
                return;

            try
            {
                if (control is WebControl webControl)
                {
                    if (!webControl.CssClass.Contains(cssClass))
                    {
                        webControl.CssClass += " " + cssClass;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'application du style CSS: {ex.Message}");
            }
        }

        /// <summary>
        /// Définit un message d'absence de données
        /// </summary>
        /// <param name="control">Le contrôle</param>
        /// <param name="message">Le message à afficher</param>
        public static void SetEmptyDataText(object control, string message)
        {
            if (control == null || string.IsNullOrEmpty(message))
                return;

            try
            {
                if (control is ListView listView)
                {
                    // Pour ListView, nous devons créer un template
                    // Cette fonctionnalité peut être étendue selon les besoins
                }
                else if (control is GridView gridView)
                {
                    gridView.EmptyDataText = message;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la définition du message vide: {ex.Message}");
            }
        }

        /// <summary>
        /// Méthode générique pour lier des données avec gestion d'erreur
        /// </summary>
        /// <typeparam name="T">Type de données</typeparam>
        /// <param name="control">Le contrôle</param>
        /// <param name="dataSource">La source de données</param>
        /// <param name="emptyMessage">Message si pas de données</param>
        public static void SafeBindData<T>(object control, IEnumerable<T> dataSource, string emptyMessage = "Aucune donnée disponible")
        {
            try
            {
                if (dataSource == null)
                {
                    ClearData(control);
                    SetEmptyDataText(control, emptyMessage);
                    return;
                }

                var dataList = dataSource as IList<T> ?? new List<T>(dataSource);
                
                if (dataList.Count == 0)
                {
                    ClearData(control);
                    SetEmptyDataText(control, emptyMessage);
                    return;
                }

                BindData(control, dataList);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la liaison sécurisée des données: {ex.Message}");
                ClearData(control);
                SetEmptyDataText(control, "Erreur lors du chargement des données");
            }
        }

        /// <summary>
        /// Applique des paramètres de pagination à un contrôle
        /// </summary>
        /// <param name="control">Le contrôle</param>
        /// <param name="pageSize">Taille de page</param>
        /// <param name="allowPaging">Autoriser la pagination</param>
        public static void ConfigurePaging(object control, int pageSize = 10, bool allowPaging = true)
        {
            try
            {
                if (control is GridView gridView)
                {
                    gridView.AllowPaging = allowPaging;
                    gridView.PageSize = pageSize;
                }
                // D'autres contrôles peuvent être ajoutés ici selon les besoins
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la configuration de la pagination: {ex.Message}");
            }
        }
    }
}
