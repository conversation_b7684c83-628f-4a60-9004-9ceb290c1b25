# ===== SCRIPT DE TEST - MESSAGERIE MODERNE LINCOM =====
# Ce script lance et teste la nouvelle interface de messagerie moderne

param(
    [switch]$Compile = $false,
    [switch]$Launch = $true,
    [switch]$OpenBrowser = $true,
    [string]$Port = "44300"
)

Write-Host "🚀 LANCEMENT DE LA MESSAGERIE MODERNE LINCOM" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour afficher les messages colorés
function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default   { Write-Host "📝 $Message" -ForegroundColor White }
    }
}

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "LinCom.csproj")) {
    Write-Status "Fichier LinCom.csproj non trouvé. Assurez-vous d'être dans le bon répertoire." "ERROR"
    exit 1
}

Write-Status "Répertoire du projet LinCom détecté" "SUCCESS"

# Vérifier les fichiers de la messagerie moderne
$fichiers = @(
    "messagerie-moderne.aspx",
    "messagerie-moderne.aspx.cs",
    "messagerie-moderne.aspx.designer.cs",
    "assets/css/messagerie-moderne.css",
    "assets/css/messagerie-animations.css",
    "assets/js/messagerie-moderne.js"
)

Write-Status "Vérification des fichiers de la messagerie moderne..." "INFO"

$fichiersManquants = @()
foreach ($fichier in $fichiers) {
    if (Test-Path $fichier) {
        Write-Status "✓ $fichier" "SUCCESS"
    } else {
        Write-Status "✗ $fichier" "ERROR"
        $fichiersManquants += $fichier
    }
}

if ($fichiersManquants.Count -gt 0) {
    Write-Status "Fichiers manquants détectés. Vérifiez l'installation." "ERROR"
    Write-Host "Fichiers manquants:"
    $fichiersManquants | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    exit 1
}

Write-Status "Tous les fichiers de la messagerie moderne sont présents" "SUCCESS"

# Compilation du projet si demandée
if ($Compile) {
    Write-Status "Compilation du projet en cours..." "INFO"
    
    try {
        # Rechercher MSBuild
        $msbuildPath = ""
        $possiblePaths = @(
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
        )
        
        foreach ($path in $possiblePaths) {
            if (Test-Path $path) {
                $msbuildPath = $path
                break
            }
        }
        
        if ($msbuildPath) {
            Write-Status "MSBuild trouvé: $msbuildPath" "INFO"
            & $msbuildPath "LinCom.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal
            
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Compilation réussie" "SUCCESS"
            } else {
                Write-Status "Erreur de compilation" "ERROR"
                exit 1
            }
        } else {
            Write-Status "MSBuild non trouvé. Utilisez Visual Studio pour compiler." "WARNING"
        }
    }
    catch {
        Write-Status "Erreur lors de la compilation: $($_.Exception.Message)" "ERROR"
    }
}

# Lancement du serveur de développement
if ($Launch) {
    Write-Status "Lancement du serveur de développement..." "INFO"
    
    try {
        # Rechercher IIS Express
        $iisExpressPath = ""
        $possiblePaths = @(
            "${env:ProgramFiles}\IIS Express\iisexpress.exe",
            "${env:ProgramFiles(x86)}\IIS Express\iisexpress.exe"
        )
        
        foreach ($path in $possiblePaths) {
            if (Test-Path $path) {
                $iisExpressPath = $path
                break
            }
        }
        
        if ($iisExpressPath) {
            Write-Status "IIS Express trouvé: $iisExpressPath" "INFO"
            Write-Status "Démarrage sur le port $Port..." "INFO"
            
            # Lancer IIS Express en arrière-plan
            $processArgs = "/path:$(Get-Location) /port:$Port"
            $process = Start-Process -FilePath $iisExpressPath -ArgumentList $processArgs -PassThru -WindowStyle Minimized
            
            Write-Status "Serveur démarré (PID: $($process.Id))" "SUCCESS"
            Write-Status "URL: http://localhost:$Port" "INFO"
            
            # Attendre que le serveur soit prêt
            Write-Status "Attente du démarrage du serveur..." "INFO"
            Start-Sleep -Seconds 3
            
            # Tester la connexion
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 10 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-Status "Serveur prêt et accessible" "SUCCESS"
                } else {
                    Write-Status "Serveur démarré mais réponse inattendue: $($response.StatusCode)" "WARNING"
                }
            }
            catch {
                Write-Status "Impossible de vérifier l'état du serveur: $($_.Exception.Message)" "WARNING"
            }
            
        } else {
            Write-Status "IIS Express non trouvé. Utilisez Visual Studio pour lancer le projet." "WARNING"
            $Launch = $false
        }
    }
    catch {
        Write-Status "Erreur lors du lancement: $($_.Exception.Message)" "ERROR"
        $Launch = $false
    }
}

# Ouverture du navigateur
if ($OpenBrowser -and $Launch) {
    Write-Status "Ouverture du navigateur..." "INFO"
    
    $urls = @(
        "http://localhost:$Port/messagerie-moderne.aspx",
        "http://localhost:$Port/test-messagerie.aspx",
        "http://localhost:$Port"
    )
    
    foreach ($url in $urls) {
        Write-Status "📱 $url" "INFO"
    }
    
    # Ouvrir la messagerie moderne
    try {
        Start-Process "http://localhost:$Port/messagerie-moderne.aspx"
        Write-Status "Messagerie moderne ouverte dans le navigateur" "SUCCESS"
    }
    catch {
        Write-Status "Erreur lors de l'ouverture du navigateur: $($_.Exception.Message)" "ERROR"
    }
}

Write-Host ""
Write-Host "🎉 MESSAGERIE MODERNE LINCOM PRÊTE !" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

if ($Launch) {
    Write-Host "📱 URLs disponibles:" -ForegroundColor Cyan
    Write-Host "   • Messagerie Moderne: http://localhost:$Port/messagerie-moderne.aspx" -ForegroundColor White
    Write-Host "   • Page de Test:       http://localhost:$Port/test-messagerie.aspx" -ForegroundColor White
    Write-Host "   • Accueil:           http://localhost:$Port" -ForegroundColor White
    Write-Host ""
}

Write-Host "🎨 Fonctionnalités disponibles:" -ForegroundColor Cyan
Write-Host "   ✅ Interface moderne et responsive" -ForegroundColor Green
Write-Host "   ✅ Chat en temps réel" -ForegroundColor Green
Write-Host "   ✅ Recherche instantanée" -ForegroundColor Green
Write-Host "   ✅ Pièces jointes et emojis" -ForegroundColor Green
Write-Host "   ✅ Notifications élégantes" -ForegroundColor Green
Write-Host "   ✅ Thème adaptatif" -ForegroundColor Green
Write-Host ""

Write-Host "📚 Documentation:" -ForegroundColor Cyan
Write-Host "   • Guide complet:      GUIDE_MESSAGERIE_MODERNE.md" -ForegroundColor White
Write-Host "   • Exemples:          EXEMPLE_UTILISATION.md" -ForegroundColor White
Write-Host "   • Résolution erreurs: GUIDE_RESOLUTION_ERREURS.md" -ForegroundColor White
Write-Host ""

Write-Host "🔧 Commandes utiles:" -ForegroundColor Cyan
Write-Host "   • Compiler:          .\TesterMessagerie.ps1 -Compile" -ForegroundColor White
Write-Host "   • Lancer seulement:  .\TesterMessagerie.ps1 -Launch" -ForegroundColor White
Write-Host "   • Port personnalisé: .\TesterMessagerie.ps1 -Port 8080" -ForegroundColor White
Write-Host ""

if ($Launch) {
    Write-Host "⚠️  Pour arrêter le serveur, fermez la fenêtre IIS Express ou utilisez Ctrl+C" -ForegroundColor Yellow
    Write-Host ""
    
    # Garder le script ouvert pour surveiller le serveur
    Write-Host "Appuyez sur une touche pour fermer ce script (le serveur continuera de fonctionner)..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

Write-Status "Script terminé avec succès" "SUCCESS"
