using System;
using System.Collections.Generic;
using System.Web;
using System.Web.Caching;

namespace LinCom.Imp
{
    /// <summary>
    /// Classe utilitaire pour la gestion du cache
    /// </summary>
    public static class CacheHelper
    {
        private static readonly Cache _cache = HttpContext.Current?.Cache ?? HttpRuntime.Cache;
        
        // Durées de cache par défaut
        private const int DEFAULT_CACHE_MINUTES = 15;
        private const int CONVERSATION_CACHE_MINUTES = 30;
        private const int MESSAGE_CACHE_MINUTES = 5;
        private const int USER_CACHE_MINUTES = 60;

        /// <summary>
        /// Ajouter un élément au cache
        /// </summary>
        /// <param name="key">Clé du cache</param>
        /// <param name="value">Valeur à mettre en cache</param>
        /// <param name="durationMinutes">Durée en minutes</param>
        public static void Set(string key, object value, int durationMinutes = DEFAULT_CACHE_MINUTES)
        {
            if (string.IsNullOrEmpty(key) || value == null)
                return;

            var expiration = DateTime.Now.AddMinutes(durationMinutes);
            _cache.Insert(key, value, null, expiration, Cache.NoSlidingExpiration, CacheItemPriority.Normal, null);
        }

        /// <summary>
        /// Récupérer un élément du cache
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="key">Clé du cache</param>
        /// <returns>Objet du cache ou null</returns>
        public static T Get<T>(string key) where T : class
        {
            if (string.IsNullOrEmpty(key))
                return null;

            return _cache.Get(key) as T;
        }

        /// <summary>
        /// Vérifier si une clé existe dans le cache
        /// </summary>
        /// <param name="key">Clé à vérifier</param>
        /// <returns>True si la clé existe</returns>
        public static bool Exists(string key)
        {
            return !string.IsNullOrEmpty(key) && _cache.Get(key) != null;
        }

        /// <summary>
        /// Supprimer un élément du cache
        /// </summary>
        /// <param name="key">Clé à supprimer</param>
        public static void Remove(string key)
        {
            if (!string.IsNullOrEmpty(key))
                _cache.Remove(key);
        }

        /// <summary>
        /// Supprimer tous les éléments du cache qui commencent par un préfixe
        /// </summary>
        /// <param name="prefix">Préfixe des clés à supprimer</param>
        public static void RemoveByPrefix(string prefix)
        {
            if (string.IsNullOrEmpty(prefix))
                return;

            var keysToRemove = new List<string>();
            var enumerator = _cache.GetEnumerator();
            
            while (enumerator.MoveNext())
            {
                if (enumerator.Key.ToString().StartsWith(prefix))
                {
                    keysToRemove.Add(enumerator.Key.ToString());
                }
            }

            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }
        }

        // Méthodes spécialisées pour les différents types de données

        /// <summary>
        /// Mettre en cache les messages d'une conversation
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        /// <param name="messages">Liste des messages</param>
        /// <param name="page">Numéro de page</param>
        public static void SetMessages(long conversationId, object messages, int page = 1)
        {
            string key = $"messages_{conversationId}_page_{page}";
            Set(key, messages, MESSAGE_CACHE_MINUTES);
        }

        /// <summary>
        /// Récupérer les messages en cache d'une conversation
        /// </summary>
        /// <typeparam name="T">Type de la liste de messages</typeparam>
        /// <param name="conversationId">ID de la conversation</param>
        /// <param name="page">Numéro de page</param>
        /// <returns>Liste des messages ou null</returns>
        public static T GetMessages<T>(long conversationId, int page = 1) where T : class
        {
            string key = $"messages_{conversationId}_page_{page}";
            return Get<T>(key);
        }

        /// <summary>
        /// Invalider le cache des messages d'une conversation
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        public static void InvalidateMessages(long conversationId)
        {
            RemoveByPrefix($"messages_{conversationId}_");
        }

        /// <summary>
        /// Mettre en cache les informations d'une conversation
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        /// <param name="conversation">Données de la conversation</param>
        public static void SetConversation(long conversationId, object conversation)
        {
            string key = $"conversation_{conversationId}";
            Set(key, conversation, CONVERSATION_CACHE_MINUTES);
        }

        /// <summary>
        /// Récupérer une conversation du cache
        /// </summary>
        /// <typeparam name="T">Type de la conversation</typeparam>
        /// <param name="conversationId">ID de la conversation</param>
        /// <returns>Conversation ou null</returns>
        public static T GetConversation<T>(long conversationId) where T : class
        {
            string key = $"conversation_{conversationId}";
            return Get<T>(key);
        }

        /// <summary>
        /// Mettre en cache les informations d'un utilisateur
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <param name="user">Données de l'utilisateur</param>
        public static void SetUser(long userId, object user)
        {
            string key = $"user_{userId}";
            Set(key, user, USER_CACHE_MINUTES);
        }

        /// <summary>
        /// Récupérer un utilisateur du cache
        /// </summary>
        /// <typeparam name="T">Type de l'utilisateur</typeparam>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>Utilisateur ou null</returns>
        public static T GetUser<T>(long userId) where T : class
        {
            string key = $"user_{userId}";
            return Get<T>(key);
        }

        /// <summary>
        /// Mettre en cache les résultats de recherche
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <param name="userId">ID de l'utilisateur qui recherche</param>
        /// <param name="results">Résultats de la recherche</param>
        public static void SetSearchResults(string searchTerm, long userId, object results)
        {
            string key = $"search_{userId}_{searchTerm.GetHashCode()}";
            Set(key, results, 10); // Cache court pour les recherches
        }

        /// <summary>
        /// Récupérer les résultats de recherche du cache
        /// </summary>
        /// <typeparam name="T">Type des résultats</typeparam>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>Résultats ou null</returns>
        public static T GetSearchResults<T>(string searchTerm, long userId) where T : class
        {
            string key = $"search_{userId}_{searchTerm.GetHashCode()}";
            return Get<T>(key);
        }

        /// <summary>
        /// Nettoyer le cache expiré (à appeler périodiquement)
        /// </summary>
        public static void CleanExpiredCache()
        {
            // Le cache ASP.NET gère automatiquement l'expiration
            // Cette méthode peut être utilisée pour un nettoyage manuel si nécessaire
            GC.Collect();
        }

        /// <summary>
        /// Obtenir des statistiques sur le cache
        /// </summary>
        /// <returns>Dictionnaire avec les statistiques</returns>
        public static Dictionary<string, object> GetCacheStats()
        {
            var stats = new Dictionary<string, object>();
            int totalItems = 0;
            long totalMemory = 0;

            var enumerator = _cache.GetEnumerator();
            while (enumerator.MoveNext())
            {
                totalItems++;
                // Estimation approximative de la taille mémoire
                if (enumerator.Value != null)
                {
                    totalMemory += enumerator.Value.ToString().Length * 2; // Approximation
                }
            }

            stats["TotalItems"] = totalItems;
            stats["EstimatedMemoryBytes"] = totalMemory;
            stats["EstimatedMemoryKB"] = totalMemory / 1024;

            return stats;
        }

        /// <summary>
        /// Vider complètement le cache
        /// </summary>
        public static void ClearAll()
        {
            var keysToRemove = new List<string>();
            var enumerator = _cache.GetEnumerator();
            
            while (enumerator.MoveNext())
            {
                keysToRemove.Add(enumerator.Key.ToString());
            }

            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }
        }
    }
}
