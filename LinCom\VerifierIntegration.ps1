# Script PowerShell pour vérifier l'intégration des améliorations LinCom
# Auteur: Assistant IA
# Date: 2025-01-21

Write-Host "🚀 Vérification de l'intégration des améliorations LinCom" -ForegroundColor Green
Write-Host "=" * 60

# Fonction pour vérifier l'existence d'un fichier
function Test-FileExists {
    param([string]$FilePath, [string]$Description)
    
    if (Test-Path $FilePath) {
        Write-Host "✅ $Description" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Description - MANQUANT" -ForegroundColor Red
        return $false
    }
}

# Fonction pour vérifier le contenu d'un fichier
function Test-FileContent {
    param([string]$FilePath, [string]$SearchPattern, [string]$Description)
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        if ($content -match $SearchPattern) {
            Write-Host "✅ $Description" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $Description - CONTENU MANQUANT" -ForegroundColor Yellow
            return $false
        }
    } else {
        Write-Host "❌ $Description - FICHIER MANQUANT" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n📁 Vérification des nouveaux fichiers..." -ForegroundColor Cyan

# Vérification des nouveaux fichiers
$newFiles = @(
    @{Path="Imp\SecurityHelper.cs"; Desc="Classe SecurityHelper"},
    @{Path="Imp\CacheHelper.cs"; Desc="Classe CacheHelper"},
    @{Path="test-messagerie.aspx"; Desc="Page de test"},
    @{Path="test-messagerie.aspx.cs"; Desc="Code-behind de test"},
    @{Path="test-messagerie.aspx.designer.cs"; Desc="Designer de test"},
    @{Path="AMELIORATIONS_MESSAGERIE.md"; Desc="Documentation des améliorations"},
    @{Path="GUIDE_VISUAL_STUDIO_2022.md"; Desc="Guide Visual Studio 2022"}
)

$allFilesExist = $true
foreach ($file in $newFiles) {
    if (-not (Test-FileExists $file.Path $file.Desc)) {
        $allFilesExist = $false
    }
}

Write-Host "`n🔧 Vérification du fichier projet..." -ForegroundColor Cyan

# Vérification du fichier projet
$projectChecks = @(
    @{Pattern="SecurityHelper\.cs"; Desc="SecurityHelper dans le projet"},
    @{Pattern="CacheHelper\.cs"; Desc="CacheHelper dans le projet"},
    @{Pattern="test-messagerie\.aspx"; Desc="Page de test dans le projet"},
    @{Pattern="test-messagerie\.aspx\.cs"; Desc="Code-behind de test dans le projet"},
    @{Pattern="AMELIORATIONS_MESSAGERIE\.md"; Desc="Documentation dans le projet"}
)

$projectFile = "LinCom.csproj"
$allProjectChecks = $true
foreach ($check in $projectChecks) {
    if (-not (Test-FileContent $projectFile $check.Pattern $check.Desc)) {
        $allProjectChecks = $false
    }
}

Write-Host "`n📝 Vérification des modifications..." -ForegroundColor Cyan

# Vérification des modifications dans les fichiers existants
$modificationChecks = @(
    @{Path="Imp\IMessage.cs"; Pattern="ValiderMessage|SanitiserContenu"; Desc="Nouvelles méthodes dans IMessage"},
    @{Path="Imp\MessageImp.cs"; Pattern="SecurityHelper|CacheHelper"; Desc="Utilisation des helpers dans MessageImp"},
    @{Path="Imp\IConversation.cs"; Pattern="RechercherMembres"; Desc="Méthode de recherche dans IConversation"},
    @{Path="Imp\ConversationImp.cs"; Pattern="RechercherMembres"; Desc="Implémentation de la recherche"},
    @{Path="messagerie.aspx"; Pattern="rechercherContacts|handleEnterKey"; Desc="JavaScript amélioré dans messagerie.aspx"},
    @{Path="messagerie.aspx.cs"; Pattern="SecurityHelper|MESSAGES_PER_PAGE"; Desc="Améliorations dans messagerie.aspx.cs"}
)

$allModifications = $true
foreach ($check in $modificationChecks) {
    if (-not (Test-FileContent $check.Path $check.Pattern $check.Desc)) {
        $allModifications = $false
    }
}

Write-Host "`n🧪 Vérification des fonctionnalités..." -ForegroundColor Cyan

# Vérification des fonctionnalités spécifiques
$featureChecks = @(
    @{Path="Imp\SecurityHelper.cs"; Pattern="ValidateMessage|SanitizeHtml|CheckRateLimit"; Desc="Fonctions de sécurité"},
    @{Path="Imp\CacheHelper.cs"; Pattern="SetMessages|GetMessages|InvalidateMessages"; Desc="Fonctions de cache"},
    @{Path="test-messagerie.aspx.cs"; Pattern="btnTestValidation_Click|btnTestCache_Click"; Desc="Tests automatisés"}
)

$allFeatures = $true
foreach ($check in $featureChecks) {
    if (-not (Test-FileContent $check.Path $check.Pattern $check.Desc)) {
        $allFeatures = $false
    }
}

Write-Host "`n📊 Résumé de la vérification..." -ForegroundColor Cyan
Write-Host "=" * 40

if ($allFilesExist) {
    Write-Host "✅ Tous les nouveaux fichiers sont présents" -ForegroundColor Green
} else {
    Write-Host "❌ Certains fichiers sont manquants" -ForegroundColor Red
}

if ($allProjectChecks) {
    Write-Host "✅ Le fichier projet est correctement configuré" -ForegroundColor Green
} else {
    Write-Host "❌ Le fichier projet nécessite des corrections" -ForegroundColor Red
}

if ($allModifications) {
    Write-Host "✅ Toutes les modifications sont présentes" -ForegroundColor Green
} else {
    Write-Host "❌ Certaines modifications sont manquantes" -ForegroundColor Red
}

if ($allFeatures) {
    Write-Host "✅ Toutes les fonctionnalités sont implémentées" -ForegroundColor Green
} else {
    Write-Host "❌ Certaines fonctionnalités sont manquantes" -ForegroundColor Red
}

Write-Host "`n🎯 Prochaines étapes..." -ForegroundColor Cyan

if ($allFilesExist -and $allProjectChecks -and $allModifications -and $allFeatures) {
    Write-Host "🎉 SUCCÈS ! Toutes les améliorations sont correctement intégrées." -ForegroundColor Green
    Write-Host ""
    Write-Host "Vous pouvez maintenant :" -ForegroundColor White
    Write-Host "1. Ouvrir LinCom.sln dans Visual Studio 2022" -ForegroundColor White
    Write-Host "2. Compiler le projet (Ctrl+Shift+B)" -ForegroundColor White
    Write-Host "3. Lancer l'application (F5)" -ForegroundColor White
    Write-Host "4. Tester les améliorations sur /test-messagerie.aspx" -ForegroundColor White
} else {
    Write-Host "⚠️ Des problèmes ont été détectés." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Actions recommandées :" -ForegroundColor White
    Write-Host "1. Vérifier les fichiers manquants" -ForegroundColor White
    Write-Host "2. Recompiler le projet" -ForegroundColor White
    Write-Host "3. Relancer ce script de vérification" -ForegroundColor White
}

Write-Host "`n📚 Documentation disponible :" -ForegroundColor Cyan
Write-Host "- AMELIORATIONS_MESSAGERIE.md : Documentation complète" -ForegroundColor White
Write-Host "- GUIDE_VISUAL_STUDIO_2022.md : Guide d'utilisation VS2022" -ForegroundColor White

Write-Host "`n" + "=" * 60
Write-Host "Vérification terminée - $(Get-Date)" -ForegroundColor Green
