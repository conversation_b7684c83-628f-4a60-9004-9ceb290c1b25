<%@ Page Title="Messagerie LinCom" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Optimisations Performance -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Préchargement des ressources critiques -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/js/main.js" as="script">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- CSS Amélioré pour la Messagerie -->
    <link href="assets/css/messagerie-amelioree.css" rel="stylesheet" type="text/css" />

    <!-- CSS Optimisé pour la Messagerie -->
    <style>
        /* Variables CSS pour cohérence */
        :root {
            --primary-color: #008374;
            --primary-hover: #006b5e;
            --secondary-color: #f4f4f4;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --border-color: #ddd;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        /* Reset et optimisations de base */
        * {
            box-sizing: border-box;
        }

        .messagerie-container {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <input type="text" id="txtRechercheContact" placeholder="Rechercher un contact..." onkeyup="rechercherContacts(this.value)">
                        <button type="button" id="btnRecherche" onclick="effectuerRecherche()">🔍</button>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# GetSecureAvatarUrl(Eval("PhotoProfil")) %>' alt='<%# "Photo de " + HttpUtility.HtmlEncode(Eval("Membre")) %>' onerror="this.src='assets/img/default-avatar.svg'" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Eval("Contenu") %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <a href='<%# Eval("AttachmentUrl") %>' target="_blank" class="attachment-link">
                        📎 Voir la pièce jointe
                    </a>
                </asp:Panel>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <!-- Zone de saisie améliorée -->
                    <div class="chat-footer">
                        <!-- Prévisualisation des pièces jointes -->
                        <div id="attachmentPreview" class="attachment-preview" style="display: none;">
                            <div class="attachment-list" id="attachmentList"></div>
                        </div>

                        <!-- Container principal de saisie -->
                        <div class="message-input-container">
                            <!-- Bouton pièce jointe -->
                            <button type="button" class="btn-attachment" onclick="openFileSelector()" title="Joindre un fichier">
                                <i class="fas fa-paperclip" aria-hidden="true"></i>
                            </button>

                            <!-- Zone de texte avec auto-resize -->
                            <div class="textarea-wrapper">
                                <textarea rows="1"
                                         runat="server"
                                         id="txtMessage"
                                         placeholder="Écrivez votre message..."
                                         maxlength="5000"
                                         aria-label="Zone de saisie du message"></textarea>

                                <!-- Indicateur de frappe -->
                                <div id="typingIndicator" class="typing-indicator" style="display: none;">
                                    <span>En cours de frappe...</span>
                                    <div class="typing-dots">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Bouton emoji -->
                            <button type="button" class="btn-emoji" onclick="toggleEmojiPicker()" title="Ajouter un emoji">
                                <i class="fas fa-smile" aria-hidden="true"></i>
                            </button>

                            <!-- Bouton d'envoi amélioré -->
                            <button type="button"
                                   runat="server"
                                   id="btnenvoie"
                                   class="btn-send"
                                   disabled
                                   title="Envoyer le message (Ctrl+Entrée)">
                                <i class="fas fa-paper-plane" aria-hidden="true"></i>
                                <span class="btn-text">Envoyer</span>
                            </button>
                        </div>

                        <!-- Actions et informations -->
                        <div class="message-footer-info">
                            <div class="left-info">
                                <span id="charCount" class="char-counter">0/5000</span>
                                <span id="messageStatus" class="message-status"></span>
                            </div>
                            <div class="right-info">
                                <span id="connectionStatus" class="connection-status">
                                    <i class="fas fa-circle" aria-hidden="true"></i>
                                    En ligne
                                </span>
                            </div>
                        </div>

                        <!-- Sélecteur d'emojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display: none;">
                            <div class="emoji-categories">
                                <button class="emoji-category active" data-category="recent">🕒</button>
                                <button class="emoji-category" data-category="smileys">😀</button>
                                <button class="emoji-category" data-category="people">👤</button>
                                <button class="emoji-category" data-category="nature">🌿</button>
                                <button class="emoji-category" data-category="food">🍕</button>
                                <button class="emoji-category" data-category="activities">⚽</button>
                                <button class="emoji-category" data-category="travel">✈️</button>
                                <button class="emoji-category" data-category="objects">💡</button>
                                <button class="emoji-category" data-category="symbols">❤️</button>
                            </div>
                            <div class="emoji-grid" id="emojiGrid">
                                <!-- Les emojis seront chargés dynamiquement -->
                            </div>
                        </div>
                    </div>

                    <!-- Input file caché pour les pièces jointes -->
                    <input type="file"
                           id="fileInput"
                           style="display: none;"
                           multiple
                           accept="image/*,document/*,.pdf,.doc,.docx,.txt,.zip,.rar"
                           onchange="handleFileSelection(this)" />

                    <!-- Champ caché pour l'ID du destinataire -->
                    <input type="hidden" id="hiddenRecipientId" value="" />
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

/* Nouveaux styles pour les améliorations */
.contacts-search {
    position: relative;
}

.contacts-search button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.message-input-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.message-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.char-counter {
    font-size: 12px;
    color: #666;
}

.char-counter.warning {
    color: #ff9800;
}

.char-counter.danger {
    color: #f44336;
}

.chat-footer button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    margin: 10px;
    border-left: 4px solid #c62828;
}

.success-message {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 10px;
    border-radius: 5px;
    margin: 10px;
    border-left: 4px solid #2e7d32;
}

    </style>

    <script type="text/javascript">
        // Configuration spécifique à la page ASP.NET
        window.aspNetConfig = {
            messageInputId: '<%= txtMessage.ClientID %>',
            sendButtonId: '<%= btnenvoie.ClientID %>'
        };

        // Fonction pour initialiser les contrôles ASP.NET
        function initializeAspNetControls() {
            console.log('🔧 Initialisation des contrôles ASP.NET...');

            const messageInput = document.getElementById(window.aspNetConfig.messageInputId);
            const sendButton = document.getElementById(window.aspNetConfig.sendButtonId);

            console.log('📝 Message Input:', messageInput);
            console.log('📤 Send Button:', sendButton);

            if (messageInput) {
                // Événement input pour activer/désactiver le bouton
                messageInput.addEventListener('input', function() {
                    console.log('✍️ Texte saisi:', this.value);
                    updateSendButtonState(this, sendButton);

                    // Appeler la fonction externe si disponible
                    if (typeof handleMessageInput === 'function') {
                        handleMessageInput(this);
                    }
                });

                // Événement keydown pour Entrée
                messageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (!sendButton.disabled) {
                            sendMessage();
                        }
                    }
                });

                // Déclencher l'événement initial
                updateSendButtonState(messageInput, sendButton);
            }

            if (sendButton) {
                sendButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🚀 Bouton envoi cliqué');
                    sendMessage();
                });
            }
        }

        // Fonction pour mettre à jour l'état du bouton d'envoi
        function updateSendButtonState(messageInput, sendButton) {
            if (!messageInput || !sendButton) return;

            const hasText = messageInput.value.trim().length > 0;
            const charCount = messageInput.value.length;

            // Mettre à jour le compteur de caractères
            const charCounter = document.getElementById('charCount');
            if (charCounter) {
                charCounter.textContent = charCount + '/5000';
                charCounter.className = 'char-counter';
                if (charCount > 4500) {
                    charCounter.classList.add('danger');
                } else if (charCount > 4000) {
                    charCounter.classList.add('warning');
                }
            }

            // Activer/désactiver le bouton
            sendButton.disabled = !hasText || charCount > 5000;

            // Changer l'apparence du bouton
            if (hasText && charCount <= 5000) {
                sendButton.classList.remove('disabled');
                sendButton.classList.add('ready');
                sendButton.style.background = 'linear-gradient(135deg, #008374, #006b5e)';
                sendButton.style.cursor = 'pointer';
                console.log('✅ Bouton activé');
            } else {
                sendButton.classList.add('disabled');
                sendButton.classList.remove('ready');
                sendButton.style.background = '#ccc';
                sendButton.style.cursor = 'not-allowed';
                console.log('❌ Bouton désactivé');
            }
        }

        // Fonction d'envoi de message RÉEL
        function sendMessage() {
            console.log('📤 Envoi du message RÉEL...');

            const messageInput = document.getElementById(window.aspNetConfig.messageInputId);
            const sendButton = document.getElementById(window.aspNetConfig.sendButtonId);

            if (!messageInput || !sendButton) {
                console.error('❌ Contrôles non trouvés');
                return;
            }

            const messageText = messageInput.value.trim();
            if (!messageText) {
                console.log('⚠️ Message vide');
                return;
            }

            console.log('📝 Message à envoyer RÉELLEMENT:', messageText);

            // Changer l'état du bouton
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span class="btn-text">Envoi...</span>';
            sendButton.style.background = '#f39c12';

            // Afficher le message immédiatement (optimiste)
            const tempMessageId = 'temp_' + Date.now();
            displayMessageOptimistic(messageText, tempMessageId);

            // Vider le champ
            messageInput.value = '';
            updateSendButtonState(messageInput, sendButton);

            // ENVOI AJAX RÉEL vers le serveur
            sendMessageToServerReal(messageText, tempMessageId, sendButton);
        }

        // Fonction d'envoi AJAX réel
        function sendMessageToServerReal(messageText, tempMessageId, sendButton) {
            console.log('🌐 Envoi AJAX vers le serveur...');

            // Obtenir l'ID du destinataire (vous devrez adapter selon votre logique)
            const recipientId = getCurrentRecipientId();

            if (!recipientId) {
                console.error('❌ Aucun destinataire sélectionné');
                handleMessageError(sendButton, 'Aucun destinataire sélectionné');
                return;
            }

            // Préparer les données
            const postData = {
                messageText: messageText,
                recipientId: recipientId.toString(),
                attachments: '[]' // Pas de pièces jointes pour l'instant
            };

            console.log('📦 Données à envoyer:', postData);

            // Appel AJAX
            fetch(window.location.pathname + '/SendMessageAjax', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(postData)
            })
            .then(response => {
                console.log('📡 Réponse serveur reçue:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('📄 Données de réponse:', data);

                if (data.d) {
                    const result = JSON.parse(data.d);
                    if (result.success) {
                        console.log('✅ Message envoyé avec succès:', result);
                        handleMessageSuccess(tempMessageId, sendButton, result.data);
                    } else {
                        console.error('❌ Erreur serveur:', result.message);
                        handleMessageError(sendButton, result.message);
                    }
                } else {
                    console.error('❌ Réponse serveur invalide:', data);
                    handleMessageError(sendButton, 'Réponse serveur invalide');
                }
            })
            .catch(error => {
                console.error('❌ Erreur AJAX:', error);
                handleMessageError(sendButton, 'Erreur de connexion: ' + error.message);
            });
        }

        // Obtenir l'ID du destinataire actuel
        function getCurrentRecipientId() {
            // Méthode 1: Depuis un élément caché
            const hiddenRecipient = document.getElementById('hiddenRecipientId');
            if (hiddenRecipient && hiddenRecipient.value) {
                return hiddenRecipient.value;
            }

            // Méthode 2: Depuis le contact sélectionné
            const selectedContact = document.querySelector('.contact-item.active');
            if (selectedContact) {
                return selectedContact.getAttribute('data-contact-id') || selectedContact.getAttribute('data-membre-id');
            }

            // Méthode 3: Depuis l'URL ou un paramètre
            const urlParams = new URLSearchParams(window.location.search);
            const recipientFromUrl = urlParams.get('recipient') || urlParams.get('id');
            if (recipientFromUrl) {
                return recipientFromUrl;
            }

            // Méthode 4: Valeur par défaut pour les tests (à adapter)
            console.warn('⚠️ Aucun destinataire trouvé, utilisation de la valeur par défaut');
            return '1'; // ID par défaut pour les tests
        }

        // Gestion du succès d'envoi
        function handleMessageSuccess(tempMessageId, sendButton, serverData) {
            console.log('🎉 Message envoyé avec succès');

            // Mettre à jour le message temporaire avec l'ID réel
            const tempMessage = document.querySelector(`[data-temp-id="${tempMessageId}"]`);
            if (tempMessage) {
                tempMessage.setAttribute('data-message-id', serverData.messageId);
                tempMessage.removeAttribute('data-temp-id');

                const statusIcon = tempMessage.querySelector('.message-status-icon');
                if (statusIcon) {
                    statusIcon.className = 'fas fa-check message-status-icon sent';
                    statusIcon.title = 'Message envoyé';
                }
            }

            // Restaurer le bouton
            sendButton.innerHTML = '<i class="fas fa-check"></i><span class="btn-text">Envoyé</span>';
            sendButton.style.background = '#27ae60';

            setTimeout(function() {
                sendButton.innerHTML = '<i class="fas fa-paper-plane"></i><span class="btn-text">Envoyer</span>';
                sendButton.disabled = false;
                updateSendButtonState(document.getElementById(window.aspNetConfig.messageInputId), sendButton);
            }, 2000);
        }

        // Gestion des erreurs d'envoi
        function handleMessageError(sendButton, errorMessage) {
            console.error('💥 Erreur d\'envoi:', errorMessage);

            // Restaurer le bouton
            sendButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span class="btn-text">Erreur</span>';
            sendButton.style.background = '#e74c3c';

            // Afficher une notification d'erreur
            showNotificationError('Erreur d\'envoi: ' + errorMessage);

            setTimeout(function() {
                sendButton.innerHTML = '<i class="fas fa-paper-plane"></i><span class="btn-text">Envoyer</span>';
                sendButton.disabled = false;
                updateSendButtonState(document.getElementById(window.aspNetConfig.messageInputId), sendButton);
            }, 3000);
        }

        // Afficher une notification d'erreur
        function showNotificationError(message) {
            const notification = document.createElement('div');
            notification.className = 'notification error';
            notification.innerHTML = `
                <i class="fas fa-exclamation-circle"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                max-width: 400px;
            `;

            document.body.appendChild(notification);

            // Suppression automatique après 5 secondes
            setTimeout(() => notification.remove(), 5000);
        }

        // Affichage optimiste du message avec ID temporaire
        function displayMessageOptimistic(messageText, tempMessageId) {
            const chatMessages = document.querySelector('.chat-messages, .chat-body');
            if (!chatMessages) {
                console.log('⚠️ Zone de messages non trouvée');
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = 'message sent new';
            messageDiv.setAttribute('data-temp-id', tempMessageId);
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="message-text">${escapeHtml(messageText)}</div>
                    </div>
                    <div class="message-info">
                        <span class="message-time">${new Date().toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</span>
                        <i class="fas fa-clock message-status-icon sending" title="Envoi en cours"></i>
                    </div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            console.log('✅ Message affiché avec ID temporaire:', tempMessageId);
        }

        // Fonction utilitaire pour échapper le HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Fonction pour sélectionner un contact
        function selectContact(contactId, contactName) {
            console.log('👤 Contact sélectionné:', contactName, 'ID:', contactId);

            // Stocker l'ID du destinataire
            const hiddenRecipient = document.getElementById('hiddenRecipientId');
            if (hiddenRecipient) {
                hiddenRecipient.value = contactId;
                console.log('💾 ID destinataire stocké:', contactId);
            }

            // Marquer le contact comme actif visuellement
            const contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(contact => {
                contact.classList.remove('active');
            });

            // Trouver et marquer le contact sélectionné
            const selectedContact = document.querySelector(`[data-contact-id="${contactId}"]`);
            if (selectedContact) {
                selectedContact.classList.add('active');
            }

            // Mettre à jour l'en-tête de conversation si nécessaire
            updateConversationHeader(contactName);
        }

        // Mettre à jour l'en-tête de conversation
        function updateConversationHeader(contactName) {
            const chatHeader = document.querySelector('.chat-header, .conversation-header');
            if (chatHeader) {
                chatHeader.innerHTML = `
                    <div class="chat-header-info">
                        <h3>Conversation avec ${escapeHtml(contactName)}</h3>
                        <span class="contact-status">En ligne</span>
                    </div>
                `;
            }
        }

        // Initialisation après chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page chargée, initialisation...');

            // Attendre un peu pour s'assurer que tout est chargé
            setTimeout(function() {
                initializeAspNetControls();
                initializeContactSelection();

                // Appeler l'initialisation du fichier externe si disponible
                if (typeof initializeMessagerie === 'function') {
                    initializeMessagerie();
                }
            }, 300);
        });

        // Initialiser la sélection de contacts
        function initializeContactSelection() {
            console.log('👥 Initialisation de la sélection de contacts...');

            // Ajouter des événements de clic aux contacts
            const contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(contact => {
                // Ajouter un attribut data-contact-id si pas présent
                const linkButton = contact.closest('a') || contact;
                const commandArg = linkButton.getAttribute('href') || '';

                // Extraire l'ID depuis l'URL ou CommandArgument
                const idMatch = commandArg.match(/id=(\d+)/);
                if (idMatch) {
                    const contactId = idMatch[1];
                    contact.setAttribute('data-contact-id', contactId);

                    // Ajouter un événement de clic
                    contact.addEventListener('click', function(e) {
                        e.preventDefault();

                        const contactName = this.querySelector('.contact-name')?.textContent || 'Contact';
                        selectContact(contactId, contactName);

                        return false;
                    });
                }
            });

            console.log(`👥 ${contacts.length} contacts initialisés`);
        }

        // ===== RÉCEPTION AUTOMATIQUE DES MESSAGES =====

        // Démarrer la vérification périodique des nouveaux messages
        function startMessagePolling() {
            console.log('🔄 Démarrage de la vérification des nouveaux messages...');

            // Vérifier immédiatement
            checkForNewMessages();

            // Puis vérifier toutes les 3 secondes
            setInterval(checkForNewMessages, 3000);
        }

        // Vérifier les nouveaux messages
        function checkForNewMessages() {
            const recipientId = getCurrentRecipientId();
            if (!recipientId) {
                return; // Pas de conversation active
            }

            // Obtenir l'ID du dernier message affiché
            const lastMessage = document.querySelector('.message:last-child');
            const lastMessageId = lastMessage ?
                (lastMessage.getAttribute('data-message-id') || '0') : '0';

            // Appel AJAX pour récupérer les nouveaux messages
            fetch(window.location.pathname + '/GetNewMessages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    conversationId: recipientId,
                    lastMessageId: lastMessageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.d) {
                    const result = JSON.parse(data.d);
                    if (result.success && result.data.hasNewMessages) {
                        console.log('📨 Nouveaux messages reçus:', result.data.messages.length);
                        displayNewMessages(result.data.messages);
                    }
                }
            })
            .catch(error => {
                console.error('❌ Erreur lors de la vérification des nouveaux messages:', error);
            });
        }

        // Afficher les nouveaux messages reçus
        function displayNewMessages(messages) {
            const chatMessages = document.querySelector('.chat-messages, .chat-body');
            if (!chatMessages) return;

            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message received new';
                messageDiv.setAttribute('data-message-id', message.id);

                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <img src="${message.senderAvatar || 'assets/img/default-avatar.svg'}" alt="Avatar" onerror="this.src='assets/img/default-avatar.svg'" />
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            <div class="message-text">${escapeHtml(message.text)}</div>
                        </div>
                        <div class="message-info">
                            <span class="message-sender">${escapeHtml(message.senderName)}</span>
                            <span class="message-time">${formatMessageTime(message.timestamp)}</span>
                        </div>
                    </div>
                `;

                chatMessages.appendChild(messageDiv);
            });

            // Faire défiler vers le bas
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Notification sonore ou visuelle
            showNewMessageNotification(messages.length);
        }

        // Formater l'heure du message
        function formatMessageTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        }

        // Afficher une notification pour les nouveaux messages
        function showNewMessageNotification(count) {
            console.log(`🔔 ${count} nouveau(x) message(s) reçu(s)`);

            // Notification visuelle simple
            const notification = document.createElement('div');
            notification.className = 'new-message-notification';
            notification.textContent = `${count} nouveau${count > 1 ? 'x' : ''} message${count > 1 ? 's' : ''}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #008374;
                color: white;
                padding: 10px 15px;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Suppression automatique après 3 secondes
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Démarrer la vérification des messages au chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(startMessagePolling, 2000); // Démarrer après 2 secondes
        });

        // Fonction de compatibilité pour l'ancien code
        function rechercherContacts(terme) {
            if (typeof rechercherContactsAmeliore === 'function') {
                rechercherContactsAmeliore(terme);
            }
        }
        // Fonction utilitaire pour faire défiler vers le bas
        function scrollToBottom() {
            const chatBody = document.querySelector('.chat-body, .chat-messages');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Appeler après le chargement de la page
        window.addEventListener('load', function() {
            scrollToBottom();
        });
    </script>

    <!-- JavaScript Amélioré pour la Messagerie -->
    <script src="assets/js/messagerie-amelioree.js" type="text/javascript"></script>

</asp:Content>
