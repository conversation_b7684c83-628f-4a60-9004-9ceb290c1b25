﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <input type="text" id="txtRechercheContact" placeholder="Rechercher un contact..." onkeyup="rechercherContacts(this.value)">
                        <button type="button" id="btnRecherche" onclick="effectuerRecherche()">🔍</button>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Eval("Contenu") %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <a href='<%# Eval("AttachmentUrl") %>' target="_blank" class="attachment-link">
                        📎 Voir la pièce jointe
                    </a>
                </asp:Panel>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <div class="message-input-container">
                            <textarea rows="2" runat="server" id="txtMessage" placeholder="Écrivez votre message..." maxlength="5000" onkeydown="handleEnterKey(event)"></textarea>
                            <div class="message-actions">
                                <span id="charCount" class="char-counter">0/5000</span>
                                <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" disabled>Envoyer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

/* Nouveaux styles pour les améliorations */
.contacts-search {
    position: relative;
}

.contacts-search button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.message-input-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.message-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.char-counter {
    font-size: 12px;
    color: #666;
}

.char-counter.warning {
    color: #ff9800;
}

.char-counter.danger {
    color: #f44336;
}

.chat-footer button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    margin: 10px;
    border-left: 4px solid #c62828;
}

.success-message {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 10px;
    border-radius: 5px;
    margin: 10px;
    border-left: 4px solid #2e7d32;
}

    </style>

    <script type="text/javascript">
        let currentPage = 1;
        let isLoading = false;
        let searchTimeout;

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            initializeMessageInput();
            initializeAutoRefresh();
        });

        // Initialiser le champ de message
        function initializeMessageInput() {
            const messageInput = document.getElementById('<%= txtMessage.ClientID %>');
            const charCounter = document.getElementById('charCount');
            const sendButton = document.getElementById('<%= btnenvoie.ClientID %>');

            if (messageInput && charCounter && sendButton) {
                messageInput.addEventListener('input', function() {
                    const length = this.value.length;
                    charCounter.textContent = length + '/5000';

                    // Changer la couleur selon la longueur
                    charCounter.className = 'char-counter';
                    if (length > 4500) {
                        charCounter.classList.add('danger');
                    } else if (length > 4000) {
                        charCounter.classList.add('warning');
                    }

                    // Activer/désactiver le bouton d'envoi
                    sendButton.disabled = length === 0 || length > 5000;
                });

                // Déclencher l'événement initial
                messageInput.dispatchEvent(new Event('input'));
            }
        }

        // Gérer la touche Entrée
        function handleEnterKey(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                const sendButton = document.getElementById('<%= btnenvoie.ClientID %>');
                if (sendButton && !sendButton.disabled) {
                    sendButton.click();
                }
            }
        }

        // Recherche de contacts avec délai
        function rechercherContacts(terme) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                if (terme.length >= 2 || terme.length === 0) {
                    effectuerRecherche(terme);
                }
            }, 300); // Délai de 300ms
        }

        // Effectuer la recherche
        function effectuerRecherche(terme) {
            if (isLoading) return;

            terme = terme || document.getElementById('txtRechercheContact').value;

            // Validation côté client
            if (terme.length > 100) {
                showMessage('Terme de recherche trop long', 'error');
                return;
            }

            isLoading = true;
            showLoading('Recherche en cours...');

            // Simulation d'appel AJAX - à remplacer par un vrai appel
            setTimeout(function() {
                hideLoading();
                // Ici, vous devriez appeler une méthode côté serveur
            }, 1000);
        }

        // Afficher un message de chargement
        function showLoading(message) {
            const chatBody = document.querySelector('.chat-body');
            if (chatBody) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading';
                loadingDiv.id = 'loadingMessage';
                loadingDiv.textContent = message;
                chatBody.appendChild(loadingDiv);
            }
        }

        // Masquer le message de chargement
        function hideLoading() {
            const loadingDiv = document.getElementById('loadingMessage');
            if (loadingDiv) {
                loadingDiv.remove();
            }
            isLoading = false;
        }

        // Afficher un message d'erreur ou de succès
        function showMessage(message, type) {
            const container = document.querySelector('.chat-wrapper');
            if (container) {
                const messageDiv = document.createElement('div');
                messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
                messageDiv.textContent = message;
                container.insertBefore(messageDiv, container.firstChild);

                // Supprimer le message après 5 secondes
                setTimeout(function() {
                    messageDiv.remove();
                }, 5000);
            }
        }

        // Auto-refresh des messages (toutes les 30 secondes)
        function initializeAutoRefresh() {
            setInterval(function() {
                const conversationId = document.getElementById('<%= lblId.ClientID %>').textContent;
                if (conversationId && conversationId !== '0') {
                    // Rafraîchir silencieusement les messages
                    location.reload();
                }
            }, 30000); // 30 secondes
        }

        // Faire défiler vers le bas automatiquement
        function scrollToBottom() {
            const chatBody = document.querySelector('.chat-body');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Appeler après le chargement de la page
        window.onload = function() {
            scrollToBottom();
        };
    </script>

</asp:Content>
