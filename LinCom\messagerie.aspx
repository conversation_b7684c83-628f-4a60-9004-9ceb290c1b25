﻿<%@ Page Title="Messagerie LinCom" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Optimisations Performance -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Préchargement des ressources critiques -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/js/main.js" as="script">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- CSS Amélioré pour la Messagerie -->
    <link href="assets/css/messagerie-amelioree.css" rel="stylesheet" type="text/css" />

    <!-- CSS Optimisé pour la Messagerie -->
    <style>
        /* Variables CSS pour cohérence */
        :root {
            --primary-color: #008374;
            --primary-hover: #006b5e;
            --secondary-color: #f4f4f4;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --border-color: #ddd;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        /* Reset et optimisations de base */
        * {
            box-sizing: border-box;
        }

        .messagerie-container {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <input type="text" id="txtRechercheContact" placeholder="Rechercher un contact..." onkeyup="rechercherContacts(this.value)">
                        <button type="button" id="btnRecherche" onclick="effectuerRecherche()">🔍</button>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Eval("Contenu") %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <a href='<%# Eval("AttachmentUrl") %>' target="_blank" class="attachment-link">
                        📎 Voir la pièce jointe
                    </a>
                </asp:Panel>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <!-- Zone de saisie améliorée -->
                    <div class="chat-footer">
                        <!-- Prévisualisation des pièces jointes -->
                        <div id="attachmentPreview" class="attachment-preview" style="display: none;">
                            <div class="attachment-list" id="attachmentList"></div>
                        </div>

                        <!-- Container principal de saisie -->
                        <div class="message-input-container">
                            <!-- Bouton pièce jointe -->
                            <button type="button" class="btn-attachment" onclick="openFileSelector()" title="Joindre un fichier">
                                <i class="fas fa-paperclip" aria-hidden="true"></i>
                            </button>

                            <!-- Zone de texte avec auto-resize -->
                            <div class="textarea-wrapper">
                                <textarea rows="1"
                                         runat="server"
                                         id="txtMessage"
                                         placeholder="Écrivez votre message..."
                                         maxlength="5000"
                                         onkeydown="handleEnterKeyImproved(event)"
                                         oninput="handleMessageInput(this)"
                                         aria-label="Zone de saisie du message"></textarea>

                                <!-- Indicateur de frappe -->
                                <div id="typingIndicator" class="typing-indicator" style="display: none;">
                                    <span>En cours de frappe...</span>
                                    <div class="typing-dots">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Bouton emoji -->
                            <button type="button" class="btn-emoji" onclick="toggleEmojiPicker()" title="Ajouter un emoji">
                                <i class="fas fa-smile" aria-hidden="true"></i>
                            </button>

                            <!-- Bouton d'envoi amélioré -->
                            <button type="button"
                                   runat="server"
                                   id="btnenvoie"
                                   class="btn-send"
                                   onclick="sendMessageImproved()"
                                   disabled
                                   title="Envoyer le message (Ctrl+Entrée)">
                                <i class="fas fa-paper-plane" aria-hidden="true"></i>
                                <span class="btn-text">Envoyer</span>
                            </button>
                        </div>

                        <!-- Actions et informations -->
                        <div class="message-footer-info">
                            <div class="left-info">
                                <span id="charCount" class="char-counter">0/5000</span>
                                <span id="messageStatus" class="message-status"></span>
                            </div>
                            <div class="right-info">
                                <span id="connectionStatus" class="connection-status">
                                    <i class="fas fa-circle" aria-hidden="true"></i>
                                    En ligne
                                </span>
                            </div>
                        </div>

                        <!-- Sélecteur d'emojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display: none;">
                            <div class="emoji-categories">
                                <button class="emoji-category active" data-category="recent">🕒</button>
                                <button class="emoji-category" data-category="smileys">😀</button>
                                <button class="emoji-category" data-category="people">👤</button>
                                <button class="emoji-category" data-category="nature">🌿</button>
                                <button class="emoji-category" data-category="food">🍕</button>
                                <button class="emoji-category" data-category="activities">⚽</button>
                                <button class="emoji-category" data-category="travel">✈️</button>
                                <button class="emoji-category" data-category="objects">💡</button>
                                <button class="emoji-category" data-category="symbols">❤️</button>
                            </div>
                            <div class="emoji-grid" id="emojiGrid">
                                <!-- Les emojis seront chargés dynamiquement -->
                            </div>
                        </div>
                    </div>

                    <!-- Input file caché pour les pièces jointes -->
                    <input type="file"
                           id="fileInput"
                           style="display: none;"
                           multiple
                           accept="image/*,document/*,.pdf,.doc,.docx,.txt,.zip,.rar"
                           onchange="handleFileSelection(this)" />
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

/* Nouveaux styles pour les améliorations */
.contacts-search {
    position: relative;
}

.contacts-search button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.message-input-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.message-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.char-counter {
    font-size: 12px;
    color: #666;
}

.char-counter.warning {
    color: #ff9800;
}

.char-counter.danger {
    color: #f44336;
}

.chat-footer button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    margin: 10px;
    border-left: 4px solid #c62828;
}

.success-message {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 10px;
    border-radius: 5px;
    margin: 10px;
    border-left: 4px solid #2e7d32;
}

    </style>

    <script type="text/javascript">
        // Configuration spécifique à la page ASP.NET
        window.aspNetConfig = {
            messageInputId: '<%= txtMessage.ClientID %>',
            sendButtonId: '<%= btnenvoie.ClientID %>'
        };

        // Initialisation après chargement du fichier externe
        document.addEventListener('DOMContentLoaded', function() {
            // Attendre que le fichier externe soit chargé
            setTimeout(function() {
                if (typeof initializeMessagerie === 'function') {
                    initializeMessagerie();
                }

                // Connecter les événements ASP.NET aux fonctions externes
                const messageInput = document.getElementById(window.aspNetConfig.messageInputId);
                const sendButton = document.getElementById(window.aspNetConfig.sendButtonId);

                if (messageInput && typeof handleMessageInput === 'function') {
                    messageInput.addEventListener('input', function() {
                        handleMessageInput(this);
                    });

                    messageInput.addEventListener('keydown', function(e) {
                        if (typeof handleEnterKeyImproved === 'function') {
                            handleEnterKeyImproved(e);
                        }
                    });
                }

                if (sendButton && typeof sendMessageImproved === 'function') {
                    sendButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        sendMessageImproved();
                    });
                }
            }, 200); // Délai pour s'assurer que le fichier externe est chargé
        });

        // Fonction de compatibilité pour l'ancien code
        function rechercherContacts(terme) {
            if (typeof rechercherContactsAmeliore === 'function') {
                rechercherContactsAmeliore(terme);
            }
        }
        // Fonction utilitaire pour faire défiler vers le bas
        function scrollToBottom() {
            const chatBody = document.querySelector('.chat-body, .chat-messages');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Appeler après le chargement de la page
        window.addEventListener('load', function() {
            scrollToBottom();
        });
    </script>

    <!-- JavaScript Amélioré pour la Messagerie -->
    <script src="assets/js/messagerie-amelioree.js" type="text/javascript"></script>

</asp:Content>
