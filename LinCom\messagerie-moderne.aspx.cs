using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using LinCom.Imp;
using LinCom.Class;
using LinCom.Model;

namespace LinCom
{
    public partial class messagerie_moderne : System.Web.UI.Page
    {
        private ConversationImp objConversation = new ConversationImp();
        private MessageImp objMessage = new MessageImp();
        private long currentUserId;

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'ID de l'utilisateur connecté
                currentUserId = GetCurrentUserId();
                hdnCurrentUserId.Value = currentUserId.ToString();

                if (!IsPostBack)
                {
                    InitialiserPage();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur Page_Load: {ex.Message}");
                AfficherErreur("Erreur lors du chargement de la page");
            }
        }

        /// <summary>
        /// Initialisation de la page
        /// </summary>
        private void InitialiserPage()
        {
            try
            {
                // Charger les conversations récentes
                ChargerConversationsRecentes();
                
                // Charger les contacts pour la modal
                ChargerContacts();
                
                // Sélectionner la première conversation si elle existe
                if (lvConversations.Items.Count > 0)
                {
                    var premiereConversation = lvConversations.Items[0];
                    var conversationId = Convert.ToInt64(premiereConversation.FindControl("hdnConversationId"));
                    hdnConversationId.Value = conversationId.ToString();
                    ChargerMessages(conversationId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur InitialiserPage: {ex.Message}");
                AfficherErreur("Erreur lors de l'initialisation");
            }
        }

        /// <summary>
        /// Charger les conversations récentes
        /// </summary>
        private void ChargerConversationsRecentes()
        {
            try
            {
                // Utiliser la méthode générique pour ListView
                objConversation.ChargerConversationsRecentesGenerique(lvConversations, currentUserId, 20);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur ChargerConversationsRecentes: {ex.Message}");
                AfficherErreur("Erreur lors du chargement des conversations");
            }
        }

        /// <summary>
        /// Charger les contacts
        /// </summary>
        private void ChargerContacts()
        {
            try
            {
                // Utiliser la méthode générique pour rechercher les membres
                objConversation.RechercherMembresGenerique(lvContacts, currentUserId, "");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur ChargerContacts: {ex.Message}");
                AfficherErreur("Erreur lors du chargement des contacts");
            }
        }

        /// <summary>
        /// Charger les messages d'une conversation
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        private void ChargerMessages(long conversationId)
        {
            try
            {
                // Utiliser la méthode générique pour ListView
                objMessage.ChargerMessagesGenerique(lvMessages, conversationId, 50, 1);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur ChargerMessages: {ex.Message}");
                AfficherErreur("Erreur lors du chargement des messages");
            }
        }

        /// <summary>
        /// Gestion de la sélection d'une conversation
        /// </summary>
        protected void lvConversations_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "SelectConversation")
                {
                    long conversationId = Convert.ToInt64(e.CommandArgument);
                    hdnConversationId.Value = conversationId.ToString();
                    ChargerMessages(conversationId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lvConversations_ItemCommand: {ex.Message}");
                AfficherErreur("Erreur lors de la sélection de la conversation");
            }
        }

        /// <summary>
        /// Gestion de la sélection d'un contact
        /// </summary>
        protected void lvContacts_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "StartConversation")
                {
                    long contactId = Convert.ToInt64(e.CommandArgument);
                    DemarrerNouvelleConversation(contactId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lvContacts_ItemCommand: {ex.Message}");
                AfficherErreur("Erreur lors de la sélection du contact");
            }
        }

        /// <summary>
        /// Démarrer une nouvelle conversation
        /// </summary>
        /// <param name="contactId">ID du contact</param>
        private void DemarrerNouvelleConversation(long contactId)
        {
            try
            {
                // Vérifier si une conversation existe déjà
                long conversationId = objConversation.ObtenirConversationPrivee(currentUserId, contactId);

                if (conversationId == 0)
                {
                    // Créer une nouvelle conversation
                    var nouvelleConversation = new Conversation_Class
                    {
                        Sujet = "Conversation privée",
                        IsGroup = 0, // 0 = conversation privée, 1 = groupe
                        CreatedAt = DateTime.Now
                    };

                    // Utiliser la méthode qui retourne directement l'ID
                    conversationId = objConversation.CreerEtRetournerID(nouvelleConversation);

                    if (conversationId > 0)
                    {
                        // Ajouter les participants
                        objConversation.AjouterParticipant(conversationId, currentUserId);
                        objConversation.AjouterParticipant(conversationId, contactId);
                    }
                }

                if (conversationId > 0)
                {
                    hdnConversationId.Value = conversationId.ToString();
                    ChargerConversationsRecentes(); // Recharger la liste
                    ChargerMessages(conversationId);

                    // Script pour fermer la modal et sélectionner la conversation
                    string script = $@"
                        fermerModal('modalNouvelleConversation');
                        setTimeout(() => {{
                            const conversation = document.querySelector('[data-conversation-id=""{conversationId}""]');
                            if (conversation) {{
                                selectionnerConversation(conversation, {conversationId});
                            }}
                        }}, 500);
                    ";
                    ClientScript.RegisterStartupScript(this.GetType(), "SelectNewConversation", script, true);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur DemarrerNouvelleConversation: {ex.Message}");
                AfficherErreur("Erreur lors de la création de la conversation");
            }
        }

        /// <summary>
        /// Envoi d'un message
        /// </summary>
        protected void btnEnvoyer_Click(object sender, EventArgs e)
        {
            try
            {
                string contenu = txtMessage.Value.Trim();
                long conversationId = Convert.ToInt64(hdnConversationId.Value);
                
                if (string.IsNullOrEmpty(contenu) || conversationId == 0)
                {
                    return;
                }
                
                // Valider le message
                if (!objMessage.ValiderMessage(contenu, currentUserId, conversationId))
                {
                    AfficherErreur("Message invalide");
                    return;
                }
                
                // Créer le message
                var nouveauMessage = new Message_Class
                {
                    ConversationId = conversationId,
                    SenderId = currentUserId,
                    Contenu = contenu,
                    DateEnvoi = DateTime.Now,
                    name = "message_text" // Type de message
                };
                
                // Envoyer le message
                int result = objMessage.Envoyer(nouveauMessage);
                
                if (result > 0)
                {
                    // Vider le champ de saisie
                    txtMessage.Value = "";
                    
                    // Recharger les messages
                    ChargerMessages(conversationId);
                    
                    // Script pour scroll automatique
                    string script = @"
                        setTimeout(() => {
                            scrollToBottom();
                            document.getElementById('txtMessage').focus();
                        }, 100);
                    ";
                    ClientScript.RegisterStartupScript(this.GetType(), "ScrollToBottom", script, true);
                }
                else
                {
                    AfficherErreur("Erreur lors de l'envoi du message");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur btnEnvoyer_Click: {ex.Message}");
                AfficherErreur("Erreur lors de l'envoi du message");
            }
        }

        /// <summary>
        /// Obtenir l'ID de l'utilisateur connecté
        /// </summary>
        /// <returns>ID de l'utilisateur</returns>
        protected long GetCurrentUserId()
        {
            // À adapter selon votre système d'authentification
            if (Session["MembreId"] != null)
            {
                return Convert.ToInt64(Session["MembreId"]);
            }
            
            // Valeur par défaut pour les tests
            return 1;
        }

        /// <summary>
        /// Afficher un message d'erreur
        /// </summary>
        /// <param name="message">Message d'erreur</param>
        private void AfficherErreur(string message)
        {
            string script = $@"
                afficherNotification('{message.Replace("'", "\\'")}', 'error');
            ";
            ClientScript.RegisterStartupScript(this.GetType(), "ShowError", script, true);
        }

        /// <summary>
        /// Afficher un message de succès
        /// </summary>
        /// <param name="message">Message de succès</param>
        private void AfficherSucces(string message)
        {
            string script = $@"
                afficherNotification('{message.Replace("'", "\\'")}', 'success');
            ";
            ClientScript.RegisterStartupScript(this.GetType(), "ShowSuccess", script, true);
        }

        /// <summary>
        /// Vérifier si une conversation privée existe déjà entre deux utilisateurs
        /// </summary>
        /// <param name="userId1">ID du premier utilisateur</param>
        /// <param name="userId2">ID du deuxième utilisateur</param>
        /// <returns>ID de la conversation ou 0 si elle n'existe pas</returns>
        private long VerifierConversationExistante(long userId1, long userId2)
        {
            try
            {
                // Utiliser la méthode existante dans ConversationImp
                return objConversation.VerifierConversationId(userId1, userId2);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur VerifierConversationExistante: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Obtenir l'ID de la dernière conversation créée pour un utilisateur
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>ID de la dernière conversation</returns>
        private long ObtenirDerniereConversation(long userId)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var derniereConversation = (from c in con.Conversations
                                               join pc in con.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                               where pc.MembreId == userId
                                               orderby c.CreatedAt descending
                                               select c.ConversationId).FirstOrDefault();

                    return derniereConversation;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur ObtenirDerniereConversation: {ex.Message}");
                return 0;
            }
        }
    }
}
