<%@ Page Title="Test Messagerie" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="test-messagerie.aspx.cs" Inherits="LinCom.test_messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container py-4">
        <h2 class="mb-4">🧪 Test des Améliorations de la Messagerie</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🔒 Tests de Sécurité</h5>
                    </div>
                    <div class="card-body">
                        <h6>Test de Validation de Message</h6>
                        <asp:TextBox ID="txtTestMessage" runat="server" TextMode="MultiLine" Rows="3" CssClass="form-control mb-2" placeholder="Entrez un message à tester..."></asp:TextBox>
                        <asp:Button ID="btnTestValidation" runat="server" Text="Tester Validation" CssClass="btn btn-primary" OnClick="btnTestValidation_Click" />
                        <div id="validationResult" runat="server" class="mt-2"></div>
                        
                        <hr />
                        
                        <h6>Test de Sanitisation HTML</h6>
                        <asp:TextBox ID="txtTestHtml" runat="server" CssClass="form-control mb-2" placeholder="<script>alert('test')</script>" />
                        <asp:Button ID="btnTestSanitize" runat="server" Text="Tester Sanitisation" CssClass="btn btn-warning" OnClick="btnTestSanitize_Click" />
                        <div id="sanitizeResult" runat="server" class="mt-2"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>⚡ Tests de Performance</h5>
                    </div>
                    <div class="card-body">
                        <h6>Test de Cache</h6>
                        <asp:Button ID="btnTestCache" runat="server" Text="Tester Cache" CssClass="btn btn-success" OnClick="btnTestCache_Click" />
                        <div id="cacheResult" runat="server" class="mt-2"></div>
                        
                        <hr />
                        
                        <h6>Statistiques du Cache</h6>
                        <asp:Button ID="btnCacheStats" runat="server" Text="Voir Statistiques" CssClass="btn btn-info" OnClick="btnCacheStats_Click" />
                        <div id="statsResult" runat="server" class="mt-2"></div>
                        
                        <hr />
                        
                        <h6>Test de Rate Limiting</h6>
                        <asp:Button ID="btnTestRateLimit" runat="server" Text="Tester Rate Limit" CssClass="btn btn-secondary" OnClick="btnTestRateLimit_Click" />
                        <div id="rateLimitResult" runat="server" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🔍 Test de Recherche</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <asp:TextBox ID="txtTestSearch" runat="server" CssClass="form-control" placeholder="Rechercher des membres..." />
                            </div>
                            <div class="col-md-4">
                                <asp:Button ID="btnTestSearch" runat="server" Text="Rechercher" CssClass="btn btn-primary w-100" OnClick="btnTestSearch_Click" />
                            </div>
                        </div>
                        <div id="searchResult" runat="server" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Résultats des Tests</h5>
                    </div>
                    <div class="card-body">
                        <asp:Literal ID="litTestResults" runat="server"></asp:Literal>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .test-success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
        }
        
        .test-warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }
        
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }
        
        .code-block {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</asp:Content>
