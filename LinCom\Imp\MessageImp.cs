﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using System.Text.RegularExpressions;
using System.Web.Security;

namespace LinCom.Imp
{
    public class MessageImp : IMessage
    {
        int msg;
        private Message message = new Message();
        private MessageStatu mesast= new MessageStatu();

        public void AfficherDetails(long messageId, Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    messageClass.MessageId = m.MessageId;
                    messageClass.ConversationId = m.ConversationId;
                    messageClass.SenderId = m.SenderId;
                    messageClass.Contenu = m.Contenu;
                    messageClass.AttachmentUrl = m.AttachmentUrl;
                    messageClass.DateEnvoi = m.DateEnvoi;
                    messageClass.name = m.name;

    }
            }
        }

      

        public void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages = 50, int page = 1)
        {
            try
            {
                var sanitizedResult = RecupererMessagesSanitises(conversationId, nombreMessages, page);
                rpt.DataSource = sanitizedResult;
                rpt.DataBind();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur dans ChargerMessages (Repeater): {ex.Message}");
                rpt.DataSource = new List<object>();
                rpt.DataBind();
            }
        }

        public void ChargerMessages(ListView lv, long conversationId, int nombreMessages = 50, int page = 1)
        {
            try
            {
                var sanitizedResult = RecupererMessagesSanitises(conversationId, nombreMessages, page);
                lv.DataSource = sanitizedResult;
                lv.DataBind();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur dans ChargerMessages (ListView): {ex.Message}");
                lv.DataSource = new List<object>();
                lv.DataBind();
            }
        }

        public int CompterNonLus(long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages
                    .Count(m => m.SenderId != membreId &&
                               con.ParticipantConversations.Any(p =>
                                   p.ConversationId == m.ConversationId &&
                                   p.MembreId == membreId));
            }
        }

        public int Envoyer(Message_Class messageClass)
        {
            // Validation des données
            if (!ValiderMessage(messageClass.Contenu, messageClass.SenderId ?? 0, messageClass.ConversationId ?? 0))
            {
                return 0;
            }

            using (Connection con = new Connection())
            {
                message.ConversationId = messageClass.ConversationId;
                message.SenderId = messageClass.SenderId;
                message.Contenu = SanitiserContenu(messageClass.Contenu);
                message.DateEnvoi = DateTime.Now;
                message.name = messageClass.name;
                message.AttachmentUrl = messageClass.AttachmentUrl;

                try
                {
                    con.Messages.Add(message);
                    int result = con.SaveChanges();

                    // Invalider le cache des messages pour cette conversation
                    if (result > 0 && messageClass.ConversationId.HasValue)
                    {
                        CacheHelper.InvalidateMessages(messageClass.ConversationId.Value);
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    // Log de l'erreur (à implémenter selon votre système de logging)
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi du message: {ex.Message}");
                    return 0;
                }
            }
        }

        public int EnvoyerMessageStatus(MessageStatus_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                mesast.MessageId = messageClass.MessageId;
                mesast.UserId = messageClass.UserId;
                mesast.IsRead = messageClass.IsRead;
                mesast.ReadAt = DateTime.Now;
              
                try
                {
                    con.MessageStatus.Add(mesast);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }



        public int Modifier(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageClass.MessageId);
                if (m != null)
                {
                    m.Contenu = messageClass.Contenu;
                    m.name = messageClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long messageId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    con.Messages.Remove(m);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }


        //Methodes pour Messages Statut

        public void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == statusId);
                if (status != null)
                {
                    statusClass.MessagestatusID = status.MessagestatusID;
                    statusClass.MessageId = status.MessageId;
                    statusClass.UserId = status.UserId;
                    statusClass.IsRead = status.IsRead;
                    statusClass.ReadAt = status.ReadAt;
                }
            }
        }

        public int MarquerCommeLu(long messageId, long userId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessageId == messageId && x.UserId == userId);
                if (status != null)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        public int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // 1. Création et ajout du message
                        var message = new Message
                        {
                            ConversationId = conversationId,
                            SenderId = senderId,
                            Contenu = contenu,
                            AttachmentUrl = attachmentUrl,
                            DateEnvoi = DateTime.Now,
                            name = "Nom ou pseudo de l'expéditeur" // adapte selon ton contexte
                        };

                        con.Messages.Add(message);
                        con.SaveChanges(); // Génère MessageId

                        // 2. Récupérer tous les participants de la conversation
                        var participants = con.ParticipantConversations
                                              .Where(pc => pc.ConversationId == conversationId)
                                              .Select(pc => pc.MembreId)
                                              .ToList();

                        // 3. Créer les MessageStatus pour tous
                        foreach (var membreId in participants)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = message.MessageId,
                                UserId = (long)membreId,
                                IsRead = (membreId == senderId) ? 1 : 0,
                                ReadAt = (membreId == senderId) ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return 1; // succès
                    }
                    catch
                    {
                        transaction.Rollback();
                        return 0; // échec
                    }
                }
            }
        }


        public int SupprimerMessageStatut(long messageStatusId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == messageStatusId);
                if (status != null)
                {
                    con.MessageStatus.Remove(status);
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        // Nouvelles méthodes pour les améliorations de sécurité et performance

        public bool ValiderMessage(string contenu, long senderId, long conversationId)
        {
            // Validation du contenu
            if (string.IsNullOrWhiteSpace(contenu))
                return false;

            if (contenu.Length > 5000) // Limite de caractères
                return false;

            // Validation de l'expéditeur
            if (senderId <= 0)
                return false;

            // Validation de la conversation
            if (conversationId <= 0)
                return false;

            // Vérifier que l'utilisateur a le droit d'écrire dans cette conversation
            return VerifierAutorisationConversation(senderId, conversationId);
        }

        public string SanitiserContenu(string contenu)
        {
            if (string.IsNullOrEmpty(contenu))
                return string.Empty;

            // Échapper les caractères HTML dangereux
            contenu = HttpUtility.HtmlEncode(contenu);

            // Supprimer les scripts potentiellement dangereux
            contenu = Regex.Replace(contenu, @"<script[^>]*>.*?</script>", "", RegexOptions.IgnoreCase);
            contenu = Regex.Replace(contenu, @"javascript:", "", RegexOptions.IgnoreCase);
            contenu = Regex.Replace(contenu, @"on\w+\s*=", "", RegexOptions.IgnoreCase);

            // Limiter la longueur
            if (contenu.Length > 5000)
                contenu = contenu.Substring(0, 5000);

            return contenu.Trim();
        }

        public bool VerifierAutorisationConversation(long membreId, long conversationId)
        {
            using (Connection con = new Connection())
            {
                // Vérifier que le membre fait partie de la conversation
                return con.ParticipantConversations.Any(pc =>
                    pc.ConversationId == conversationId && pc.MembreId == membreId);
            }
        }

        public int CompterMessagesConversation(long conversationId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages.Count(m => m.ConversationId == conversationId);
            }
        }

        public void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int pageSize, int pageNumber)
        {
            // Vérifier le cache d'abord
            var cachedMessages = CacheHelper.GetMessages<List<object>>(conversationId, pageNumber);
            if (cachedMessages != null)
            {
                rpt.DataSource = cachedMessages;
                rpt.DataBind();
                return;
            }

            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""
                               };

                int skip = (pageNumber - 1) * pageSize;
                var result = messages.Skip(skip).Take(pageSize).ToList();

                // Sanitiser le contenu APRÈS avoir récupéré les données
                var sanitizedResult = result.Select(m => new
                {
                    id = m.id,
                    Contenu = SanitiserContenu(m.Contenu),
                    Expediteur = m.Expediteur,
                    Photomembre = m.Photomembre,
                    DateEnvoi = m.DateEnvoi,
                    name = m.name,
                    AttachmentUrl = m.AttachmentUrl
                }).ToList();

                // Mettre en cache les résultats sanitisés
                CacheHelper.SetMessages(conversationId, sanitizedResult, pageNumber);

                rpt.DataSource = sanitizedResult;
                rpt.DataBind();
            }
        }

        public void ChargerMessagesAvecPagination(ListView lv, long conversationId, int pageSize, int pageNumber)
        {
            // Vérifier le cache d'abord
            var cachedMessages = CacheHelper.GetMessages<List<object>>(conversationId, pageNumber);
            if (cachedMessages != null)
            {
                lv.DataSource = cachedMessages;
                lv.DataBind();
                return;
            }

            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""
                               };

                int skip = (pageNumber - 1) * pageSize;
                var result = messages.Skip(skip).Take(pageSize).ToList();

                // Sanitiser le contenu APRÈS avoir récupéré les données
                var sanitizedResult = result.Select(m => new
                {
                    id = m.id,
                    Contenu = SanitiserContenu(m.Contenu),
                    Expediteur = m.Expediteur,
                    Photomembre = m.Photomembre,
                    DateEnvoi = m.DateEnvoi,
                    name = m.name,
                    AttachmentUrl = m.AttachmentUrl
                }).ToList();

                // Mettre en cache les résultats sanitisés
                CacheHelper.SetMessages(conversationId, sanitizedResult, pageNumber);

                lv.DataSource = sanitizedResult;
                lv.DataBind();
            }
        }

        // Méthode générique pour tous types de contrôles
        public void ChargerMessagesGenerique(object control, long conversationId, int pageSize, int pageNumber)
        {
            if (!DataControlHelper.IsSupportedDataControl(control))
            {
                throw new ArgumentException($"Type de contrôle non supporté: {DataControlHelper.GetControlTypeName(control)}");
            }

            try
            {
                // Vérifier le cache d'abord
                var cachedMessages = CacheHelper.GetMessages<List<object>>(conversationId, pageNumber);
                if (cachedMessages != null)
                {
                    DataControlHelper.SafeBindData(control, cachedMessages, "Aucun message trouvé");
                    return;
                }

                using (Connection con = new Connection())
                {
                    var messages = from m in con.Messages
                                   join mb in con.Membres on m.SenderId equals mb.MembreId
                                   where m.ConversationId == conversationId
                                   orderby m.DateEnvoi descending
                                   select new
                                   {
                                       id = m.MessageId,
                                       Contenu = m.Contenu ?? "",
                                       Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                       Photomembre = mb.PhotoProfil ?? "",
                                       DateEnvoi = m.DateEnvoi,
                                       name = m.name ?? "",
                                       AttachmentUrl = m.AttachmentUrl ?? ""
                                   };

                    int skip = (pageNumber - 1) * pageSize;
                    var result = messages.Skip(skip).Take(pageSize).ToList();

                    // Sanitiser le contenu APRÈS avoir récupéré les données
                    var sanitizedResult = result.Select(m => new
                    {
                        id = m.id,
                        Contenu = SanitiserContenu(m.Contenu),
                        Expediteur = m.Expediteur,
                        Photomembre = m.Photomembre,
                        DateEnvoi = m.DateEnvoi,
                        name = m.name,
                        AttachmentUrl = m.AttachmentUrl
                    }).ToList();

                    // Mettre en cache les résultats sanitisés
                    CacheHelper.SetMessages(conversationId, sanitizedResult, pageNumber);

                    DataControlHelper.SafeBindData(control, sanitizedResult, "Aucun message trouvé");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur dans ChargerMessagesGenerique: {ex.Message}");
                DataControlHelper.SafeBindData(control, new List<object>(), "Erreur lors du chargement des messages");
            }
        }

        // Méthode utilitaire privée pour récupérer et sanitiser les messages
        private List<object> RecupererMessagesSanitises(long conversationId, int pageSize, int pageNumber)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""
                               };

                int skip = (pageNumber - 1) * pageSize;
                var result = messages.Skip(skip).Take(pageSize).ToList();

                // Sanitiser le contenu APRÈS avoir récupéré les données
                var sanitizedResult = result.Select(m => new
                {
                    id = m.id,
                    Contenu = SanitiserContenu(m.Contenu),
                    Expediteur = m.Expediteur,
                    Photomembre = m.Photomembre,
                    DateEnvoi = m.DateEnvoi,
                    name = m.name,
                    AttachmentUrl = m.AttachmentUrl
                }).Cast<object>().ToList();

                return sanitizedResult;
            }
        }

    }
}