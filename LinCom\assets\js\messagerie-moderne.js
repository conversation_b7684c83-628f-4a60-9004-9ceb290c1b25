/**
 * MESSAGERIE MODERNE - JavaScript
 * Fonctionnalités interactives pour l'interface de messagerie
 */

// Variables globales
let currentConversationId = null;
let currentUserId = null;
let isTyping = false;
let typingTimeout = null;
let autoRefreshInterval = null;
let lastMessageId = null;

// Configuration
const CONFIG = {
    AUTO_REFRESH_INTERVAL: 5000, // 5 secondes
    TYPING_TIMEOUT: 3000, // 3 secondes
    MAX_MESSAGE_LENGTH: 5000,
    SEARCH_DELAY: 300, // 300ms
    ANIMATION_DURATION: 300
};

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    setupEventListeners();
    startAutoRefresh();
});

/**
 * Initialisation du chat
 */
function initializeChat() {
    // Récupérer l'ID utilisateur actuel
    const userIdField = document.getElementById('hdnCurrentUserId');
    if (userIdField) {
        currentUserId = userIdField.value;
    }
    
    // Initialiser la première conversation si elle existe
    const firstConversation = document.querySelector('.conversation-item.active');
    if (firstConversation) {
        const conversationId = firstConversation.dataset.conversationId;
        if (conversationId) {
            currentConversationId = conversationId;
            chargerConversation(conversationId);
        }
    }
    
    // Configurer le textarea auto-resize
    setupAutoResizeTextarea();
    
    // Initialiser le compteur de caractères
    updateCharCounter();
    
    console.log('💬 Messagerie moderne initialisée');
}

/**
 * Configuration des écouteurs d'événements
 */
function setupEventListeners() {
    // Recherche de conversations
    const searchInput = document.getElementById('txtRechercheConversation');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                rechercherConversations(this.value);
            }, CONFIG.SEARCH_DELAY);
        });
    }
    
    // Zone de saisie
    const messageInput = document.getElementById('txtMessage');
    if (messageInput) {
        messageInput.addEventListener('input', function() {
            updateCharCounter();
            handleTyping();
        });
        
        messageInput.addEventListener('keydown', function(e) {
            gererToucheEntree(e);
        });
        
        messageInput.addEventListener('paste', function(e) {
            setTimeout(() => {
                updateCharCounter();
                autoResizeTextarea(this);
            }, 10);
        });
    }
    
    // Boutons d'action
    setupActionButtons();
    
    // Gestion des modals
    setupModalHandlers();
    
    // Gestion du scroll automatique
    setupAutoScroll();
}

/**
 * Configuration du textarea auto-resize
 */
function setupAutoResizeTextarea() {
    const textarea = document.getElementById('txtMessage');
    if (textarea) {
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
    }
}

/**
 * Auto-resize du textarea
 */
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    const newHeight = Math.min(textarea.scrollHeight, 120); // Max 120px
    textarea.style.height = newHeight + 'px';
    
    // Ajuster le container parent si nécessaire
    const inputWrapper = textarea.closest('.input-wrapper');
    if (inputWrapper) {
        inputWrapper.style.alignItems = newHeight > 40 ? 'flex-end' : 'center';
    }
}

/**
 * Mise à jour du compteur de caractères
 */
function updateCharCounter() {
    const messageInput = document.getElementById('txtMessage');
    const charCounter = document.getElementById('charCounter');
    const sendButton = document.getElementById('btnEnvoyer');
    
    if (messageInput && charCounter) {
        const length = messageInput.value.length;
        charCounter.textContent = `${length}/${CONFIG.MAX_MESSAGE_LENGTH}`;
        
        // Changer la couleur selon la longueur
        charCounter.className = 'char-counter';
        if (length > CONFIG.MAX_MESSAGE_LENGTH * 0.9) {
            charCounter.classList.add('danger');
        } else if (length > CONFIG.MAX_MESSAGE_LENGTH * 0.8) {
            charCounter.classList.add('warning');
        }
        
        // Activer/désactiver le bouton d'envoi
        if (sendButton) {
            sendButton.disabled = length === 0 || length > CONFIG.MAX_MESSAGE_LENGTH;
        }
    }
}

/**
 * Gestion de la touche Entrée
 */
function gererToucheEntree(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        const sendButton = document.getElementById('btnEnvoyer');
        if (sendButton && !sendButton.disabled) {
            envoyerMessage();
        }
    }
}

/**
 * Gestion de l'indicateur de frappe
 */
function handleTyping() {
    if (!isTyping && currentConversationId) {
        isTyping = true;
        // Envoyer signal de début de frappe
        envoyerSignalFrappe(true);
    }
    
    // Reset du timeout
    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
        if (isTyping) {
            isTyping = false;
            // Envoyer signal de fin de frappe
            envoyerSignalFrappe(false);
        }
    }, CONFIG.TYPING_TIMEOUT);
}

/**
 * Sélection d'une conversation
 */
function selectionnerConversation(element, conversationId) {
    // Retirer la classe active de toutes les conversations
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Ajouter la classe active à la conversation sélectionnée
    element.classList.add('active');
    
    // Mettre à jour l'ID de conversation actuel
    currentConversationId = conversationId;
    
    // Charger la conversation
    chargerConversation(conversationId);
    
    // Mettre à jour les champs cachés
    const conversationIdField = document.getElementById('hdnConversationId');
    if (conversationIdField) {
        conversationIdField.value = conversationId;
    }
    
    // Animation de sélection
    element.style.transform = 'scale(0.98)';
    setTimeout(() => {
        element.style.transform = 'scale(1)';
    }, 150);
}

/**
 * Chargement d'une conversation
 */
function chargerConversation(conversationId) {
    if (!conversationId) return;
    
    // Afficher un indicateur de chargement
    afficherChargement();
    
    // Simuler le chargement (à remplacer par un appel AJAX réel)
    setTimeout(() => {
        masquerChargement();
        scrollToBottom();
        
        // Mettre à jour les informations du contact
        mettreAJourInfosContact(conversationId);
    }, 500);
}

/**
 * Mise à jour des informations du contact
 */
function mettreAJourInfosContact(conversationId) {
    const conversationElement = document.querySelector(`[data-conversation-id="${conversationId}"]`);
    if (conversationElement) {
        const contactName = conversationElement.querySelector('.conversation-name').textContent;
        const contactAvatar = conversationElement.querySelector('.conversation-avatar img').src;
        
        // Mettre à jour l'en-tête du chat
        const chatContactName = document.getElementById('chatContactName');
        const chatContactAvatar = document.getElementById('chatContactAvatar');
        const chatContactStatus = document.getElementById('chatContactStatusText');
        
        if (chatContactName) chatContactName.textContent = contactName;
        if (chatContactAvatar) chatContactAvatar.src = contactAvatar;
        if (chatContactStatus) chatContactStatus.textContent = 'En ligne'; // À adapter
    }
}

/**
 * Recherche de conversations
 */
function rechercherConversations(terme) {
    const conversations = document.querySelectorAll('.conversation-item');
    const clearButton = document.querySelector('.clear-search');
    
    if (terme.length === 0) {
        // Afficher toutes les conversations
        conversations.forEach(conv => {
            conv.style.display = 'flex';
        });
        if (clearButton) clearButton.style.display = 'none';
        return;
    }
    
    if (clearButton) clearButton.style.display = 'block';
    
    // Filtrer les conversations
    conversations.forEach(conv => {
        const name = conv.querySelector('.conversation-name').textContent.toLowerCase();
        const lastMessage = conv.querySelector('.last-message').textContent.toLowerCase();
        const searchTerm = terme.toLowerCase();
        
        if (name.includes(searchTerm) || lastMessage.includes(searchTerm)) {
            conv.style.display = 'flex';
            // Surligner le terme recherché
            highlightSearchTerm(conv, searchTerm);
        } else {
            conv.style.display = 'none';
        }
    });
}

/**
 * Vider la recherche
 */
function viderRecherche() {
    const searchInput = document.getElementById('txtRechercheConversation');
    const clearButton = document.querySelector('.clear-search');
    
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }
    
    if (clearButton) {
        clearButton.style.display = 'none';
    }
    
    // Réafficher toutes les conversations
    rechercherConversations('');
}

/**
 * Surligner le terme recherché
 */
function highlightSearchTerm(element, term) {
    // Implémentation simple - peut être améliorée
    const nameElement = element.querySelector('.conversation-name');
    const messageElement = element.querySelector('.last-message');
    
    [nameElement, messageElement].forEach(el => {
        if (el) {
            const text = el.textContent;
            const regex = new RegExp(`(${term})`, 'gi');
            const highlightedText = text.replace(regex, '<mark>$1</mark>');
            el.innerHTML = highlightedText;
        }
    });
}

/**
 * Scroll automatique vers le bas
 */
function scrollToBottom(smooth = true) {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        const scrollOptions = {
            top: chatMessages.scrollHeight,
            behavior: smooth ? 'smooth' : 'auto'
        };
        chatMessages.scrollTo(scrollOptions);
    }
}

/**
 * Configuration du scroll automatique
 */
function setupAutoScroll() {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        // Observer les nouveaux messages
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Nouveau message ajouté, scroll vers le bas
                    setTimeout(() => scrollToBottom(), 100);
                }
            });
        });
        
        observer.observe(chatMessages, {
            childList: true,
            subtree: true
        });
    }
}

/**
 * Affichage du chargement
 */
function afficherChargement() {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loadingIndicator';
        loadingDiv.className = 'loading-indicator';
        loadingDiv.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Chargement...</span>
            </div>
        `;
        chatMessages.appendChild(loadingDiv);
    }
}

/**
 * Masquer le chargement
 */
function masquerChargement() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.remove();
    }
}

/**
 * Envoi d'un message
 */
function envoyerMessage() {
    const messageInput = document.getElementById('txtMessage');
    if (!messageInput || !currentConversationId) return;

    const message = messageInput.value.trim();
    if (message.length === 0 || message.length > CONFIG.MAX_MESSAGE_LENGTH) return;

    // Désactiver temporairement l'envoi
    const sendButton = document.getElementById('btnEnvoyer');
    if (sendButton) {
        sendButton.disabled = true;
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    // Ajouter le message à l'interface immédiatement (optimistic UI)
    ajouterMessageOptimiste(message);

    // Vider le champ de saisie
    messageInput.value = '';
    updateCharCounter();
    autoResizeTextarea(messageInput);

    // Simuler l'envoi (à remplacer par un appel AJAX réel)
    setTimeout(() => {
        if (sendButton) {
            sendButton.disabled = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        }

        // Scroll vers le bas
        scrollToBottom();

        // Remettre le focus sur l'input
        messageInput.focus();
    }, 1000);
}

/**
 * Ajouter un message de manière optimiste
 */
function ajouterMessageOptimiste(contenu) {
    const messagesContainer = document.querySelector('.messages-container');
    if (!messagesContainer) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message sent';
    messageDiv.innerHTML = `
        <div class="message-avatar">
            <img src="assets/img/default-avatar.png" alt="Avatar">
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <div class="message-text">${escapeHtml(contenu)}</div>
            </div>
            <div class="message-info">
                <span class="message-time">${new Date().toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</span>
                <i class="fas fa-clock message-status sending"></i>
            </div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);

    // Animation d'apparition
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';
    setTimeout(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    }, 10);
}

/**
 * Échapper le HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Configuration des boutons d'action
 */
function setupActionButtons() {
    // Bouton pièce jointe
    const attachmentBtn = document.querySelector('.btn-attachment');
    if (attachmentBtn) {
        attachmentBtn.addEventListener('click', ouvrirSelecteurFichier);
    }

    // Bouton emoji
    const emojiBtn = document.querySelector('.btn-emoji');
    if (emojiBtn) {
        emojiBtn.addEventListener('click', ouvrirEmojis);
    }
}

/**
 * Ouvrir le sélecteur de fichier
 */
function ouvrirSelecteurFichier() {
    // Créer un input file temporaire
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*,document/*,.pdf,.doc,.docx,.txt';
    fileInput.multiple = false;

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            gererFichierSelectionne(file);
        }
    });

    fileInput.click();
}

/**
 * Gérer le fichier sélectionné
 */
function gererFichierSelectionne(file) {
    // Vérifier la taille du fichier (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        afficherNotification('Le fichier est trop volumineux (max 10MB)', 'error');
        return;
    }

    // Afficher une prévisualisation
    afficherPreviewFichier(file);
}

/**
 * Afficher la prévisualisation du fichier
 */
function afficherPreviewFichier(file) {
    const messageInput = document.getElementById('txtMessage');
    if (!messageInput) return;

    // Créer un élément de prévisualisation
    const preview = document.createElement('div');
    preview.className = 'file-preview';
    preview.innerHTML = `
        <div class="file-info">
            <i class="fas fa-${getFileIcon(file.type)}"></i>
            <span class="file-name">${file.name}</span>
            <span class="file-size">(${formatFileSize(file.size)})</span>
            <button class="btn-remove-file" onclick="supprimerPreviewFichier()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Insérer avant la zone de saisie
    const inputContainer = messageInput.closest('.chat-input-container');
    inputContainer.insertBefore(preview, inputContainer.firstChild);
}

/**
 * Obtenir l'icône du fichier selon son type
 */
function getFileIcon(fileType) {
    if (fileType.startsWith('image/')) return 'image';
    if (fileType.includes('pdf')) return 'file-pdf';
    if (fileType.includes('word') || fileType.includes('document')) return 'file-word';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return 'file-excel';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return 'file-powerpoint';
    return 'file';
}

/**
 * Formater la taille du fichier
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Supprimer la prévisualisation du fichier
 */
function supprimerPreviewFichier() {
    const preview = document.querySelector('.file-preview');
    if (preview) {
        preview.remove();
    }
}

/**
 * Ouvrir le sélecteur d'emojis
 */
function ouvrirEmojis() {
    // Implémentation simple - peut être remplacée par une bibliothèque d'emojis
    const emojis = ['😀', '😂', '😍', '🤔', '😢', '😡', '👍', '👎', '❤️', '🎉', '🔥', '💯'];

    // Créer un popup d'emojis
    const emojiPopup = document.createElement('div');
    emojiPopup.className = 'emoji-popup';
    emojiPopup.innerHTML = emojis.map(emoji =>
        `<button class="emoji-btn" onclick="insererEmoji('${emoji}')">${emoji}</button>`
    ).join('');

    // Positionner le popup
    const emojiBtn = document.querySelector('.btn-emoji');
    if (emojiBtn) {
        const rect = emojiBtn.getBoundingClientRect();
        emojiPopup.style.position = 'absolute';
        emojiPopup.style.bottom = '60px';
        emojiPopup.style.right = '60px';
        emojiPopup.style.zIndex = '1000';

        // Ajouter au DOM
        emojiBtn.closest('.chat-input-container').appendChild(emojiPopup);

        // Fermer au clic extérieur
        setTimeout(() => {
            document.addEventListener('click', function fermerEmojis(e) {
                if (!emojiPopup.contains(e.target) && e.target !== emojiBtn) {
                    emojiPopup.remove();
                    document.removeEventListener('click', fermerEmojis);
                }
            });
        }, 100);
    }
}

/**
 * Insérer un emoji dans le message
 */
function insererEmoji(emoji) {
    const messageInput = document.getElementById('txtMessage');
    if (messageInput) {
        const cursorPos = messageInput.selectionStart;
        const textBefore = messageInput.value.substring(0, cursorPos);
        const textAfter = messageInput.value.substring(messageInput.selectionEnd);

        messageInput.value = textBefore + emoji + textAfter;
        messageInput.selectionStart = messageInput.selectionEnd = cursorPos + emoji.length;

        updateCharCounter();
        autoResizeTextarea(messageInput);
        messageInput.focus();
    }

    // Fermer le popup
    const emojiPopup = document.querySelector('.emoji-popup');
    if (emojiPopup) {
        emojiPopup.remove();
    }
}

/**
 * Gestion des modals
 */
function setupModalHandlers() {
    // Fermer les modals au clic sur l'overlay
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-overlay')) {
            fermerModal(e.target.id);
        }
    });

    // Fermer les modals avec la touche Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                if (modal.style.display !== 'none') {
                    fermerModal(modal.id);
                }
            });
        }
    });
}

/**
 * Ouvrir une modal
 */
function ouvrirModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.add('fade-in');

        // Focus sur le premier élément focusable
        setTimeout(() => {
            const firstInput = modal.querySelector('input, button, textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }
}

/**
 * Fermer une modal
 */
function fermerModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('fade-out');
        setTimeout(() => {
            modal.style.display = 'none';
            modal.classList.remove('fade-in', 'fade-out');
        }, 300);
    }
}

/**
 * Ouvrir la modal de nouvelle conversation
 */
function ouvrirNouvelleConversation() {
    ouvrirModal('modalNouvelleConversation');
}

/**
 * Ouvrir le menu des options
 */
function ouvrirOptions() {
    // Implémentation du menu des options
    afficherNotification('Fonctionnalité en développement', 'info');
}

/**
 * Démarrer une conversation avec un contact
 */
function demarrerConversation(contactId) {
    // Déclencher l'événement côté serveur
    __doPostBack('lvContacts$ctrl0$btnStartConversation', contactId);
}

/**
 * Fermer le panel d'informations
 */
function fermerInfoPanel() {
    const infoPanel = document.getElementById('chatInfoPanel');
    if (infoPanel) {
        infoPanel.style.display = 'none';
    }
}

/**
 * Afficher une notification
 */
function afficherNotification(message, type = 'info', title = null, duration = 5000) {
    // Créer le container de notifications s'il n'existe pas
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Icône selon le type
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    // Titre par défaut selon le type
    const defaultTitles = {
        success: 'Succès',
        error: 'Erreur',
        warning: 'Attention',
        info: 'Information'
    };

    const finalTitle = title || defaultTitles[type] || 'Notification';

    notification.innerHTML = `
        <div class="notification-icon">
            <i class="${icons[type] || icons.info}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-title">${finalTitle}</div>
            <div class="notification-message">${message}</div>
        </div>
        <button class="notification-close" onclick="fermerNotification(this)">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Ajouter au container
    container.appendChild(notification);

    // Auto-suppression après la durée spécifiée
    if (duration > 0) {
        setTimeout(() => {
            fermerNotification(notification.querySelector('.notification-close'));
        }, duration);
    }

    return notification;
}

/**
 * Fermer une notification
 */
function fermerNotification(button) {
    const notification = button.closest('.notification');
    if (notification) {
        notification.classList.add('removing');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
}

/**
 * Envoyer un signal de frappe
 */
function envoyerSignalFrappe(isTyping) {
    if (!currentConversationId) return;

    // Implémentation avec SignalR ou AJAX
    console.log(`Signal de frappe: ${isTyping ? 'début' : 'fin'} pour conversation ${currentConversationId}`);

    // Afficher/masquer l'indicateur de frappe pour les autres utilisateurs
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator && !isTyping) {
        // Simuler la réception d'un signal de frappe d'un autre utilisateur
        // typingIndicator.style.display = isTyping ? 'flex' : 'none';
    }
}

/**
 * Démarrage de l'actualisation automatique
 */
function startAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    autoRefreshInterval = setInterval(() => {
        if (currentConversationId) {
            // Vérifier s'il y a de nouveaux messages
            verifierNouveauxMessages();
        }
    }, CONFIG.AUTO_REFRESH_INTERVAL);
}

/**
 * Arrêt de l'actualisation automatique
 */
function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

/**
 * Vérifier les nouveaux messages
 */
function verifierNouveauxMessages() {
    // Implémentation avec AJAX pour vérifier les nouveaux messages
    // Cette fonction devrait faire un appel au serveur pour récupérer les nouveaux messages
    console.log('Vérification des nouveaux messages...');
}

/**
 * Rechercher des contacts
 */
function rechercherContacts(terme) {
    const contacts = document.querySelectorAll('.contact-item');

    contacts.forEach(contact => {
        const name = contact.querySelector('.contact-info h4').textContent.toLowerCase();
        const email = contact.querySelector('.contact-info p').textContent.toLowerCase();
        const searchTerm = terme.toLowerCase();

        if (name.includes(searchTerm) || email.includes(searchTerm)) {
            contact.style.display = 'flex';
        } else {
            contact.style.display = 'none';
        }
    });
}

/**
 * Gestion de la saisie
 */
function gererSaisie() {
    const messageInput = document.getElementById('txtMessage');
    if (messageInput) {
        updateCharCounter();
        autoResizeTextarea(messageInput);
        handleTyping();
    }
}

/**
 * Utilitaires pour les animations
 */
const AnimationUtils = {
    /**
     * Ajouter une classe d'animation
     */
    addAnimation: function(element, animationClass, callback) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (element) {
            element.classList.add(animationClass);

            const handleAnimationEnd = () => {
                element.classList.remove(animationClass);
                element.removeEventListener('animationend', handleAnimationEnd);
                if (callback) callback();
            };

            element.addEventListener('animationend', handleAnimationEnd);
        }
    },

    /**
     * Faire clignoter un élément
     */
    pulse: function(element, duration = 2000) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (element) {
            element.classList.add('pulse');
            setTimeout(() => {
                element.classList.remove('pulse');
            }, duration);
        }
    },

    /**
     * Secouer un élément
     */
    shake: function(element) {
        this.addAnimation(element, 'shake');
    },

    /**
     * Faire rebondir un élément
     */
    bounce: function(element) {
        this.addAnimation(element, 'bounce');
    }
};

/**
 * Nettoyage lors de la fermeture de la page
 */
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();

    // Envoyer un signal de déconnexion si nécessaire
    if (isTyping) {
        envoyerSignalFrappe(false);
    }
});

/**
 * Gestion des erreurs globales
 */
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript:', e.error);
    afficherNotification('Une erreur inattendue s\'est produite', 'error');
});

// Export des fonctions pour utilisation globale
window.MessagerieFunctions = {
    selectionnerConversation,
    rechercherConversations,
    viderRecherche,
    ouvrirNouvelleConversation,
    ouvrirOptions,
    demarrerConversation,
    fermerInfoPanel,
    fermerModal,
    ouvrirModal,
    afficherNotification,
    fermerNotification,
    gererToucheEntree,
    gererSaisie,
    envoyerMessage,
    ouvrirEmojis,
    insererEmoji,
    AnimationUtils
};
