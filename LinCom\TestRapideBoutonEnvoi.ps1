# ===== TEST RAPIDE BOUTON ENVOI - MESSAGERIE LINCOM =====
# Script pour tester rapidement le bouton d'envoi

Write-Host "🔧 TEST RAPIDE BOUTON ENVOI - MESSAGERIE LINCOM" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour afficher les messages colorés
function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default   { Write-Host "📝 $Message" -ForegroundColor White }
    }
}

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "LinCom.csproj")) {
    Write-Status "Fichier LinCom.csproj non trouvé. Assurez-vous d'être dans le bon répertoire." "ERROR"
    exit 1
}

Write-Status "Répertoire du projet LinCom détecté" "SUCCESS"

# Vérifier les corrections appliquées
Write-Host ""
Write-Status "Vérification des corrections appliquées..." "INFO"

$corrections = @(
    @{File="messagerie.aspx"; Pattern="window\.aspNetConfig"; Desc="Configuration ASP.NET JavaScript"},
    @{File="messagerie.aspx"; Pattern="initializeAspNetControls"; Desc="Fonction d'initialisation des contrôles"},
    @{File="messagerie.aspx"; Pattern="updateSendButtonState"; Desc="Fonction de mise à jour du bouton"},
    @{File="assets/css/messagerie-amelioree.css"; Pattern="\.btn-send\.ready"; Desc="Styles CSS pour bouton activé"},
    @{File="assets/css/messagerie-amelioree.css"; Pattern="\.btn-send\.sending"; Desc="Styles CSS pour envoi en cours"},
    @{File="TesterBoutonEnvoi.html"; Pattern="Test Bouton Envoi"; Desc="Page de test interactive"}
)

$correctionsOK = 0
foreach ($correction in $corrections) {
    if (Test-Path $correction.File) {
        $content = Get-Content $correction.File -Raw -ErrorAction SilentlyContinue
        if ($content -and $content -match $correction.Pattern) {
            Write-Status "✓ $($correction.Desc)" "SUCCESS"
            $correctionsOK++
        } else {
            Write-Status "✗ $($correction.Desc) - Pattern non trouvé" "WARNING"
        }
    } else {
        Write-Status "✗ $($correction.Desc) - Fichier manquant" "ERROR"
    }
}

Write-Host ""
Write-Status "Corrections détectées: $correctionsOK/$($corrections.Count)" "INFO"

# Ouvrir la page de test
Write-Host ""
$response = Read-Host "Voulez-vous ouvrir la page de test interactive ? (O/N)"

if ($response -eq "O" -or $response -eq "o" -or $response -eq "Y" -or $response -eq "y") {
    if (Test-Path "TesterBoutonEnvoi.html") {
        try {
            Start-Process "TesterBoutonEnvoi.html"
            Write-Status "Page de test ouverte dans le navigateur" "SUCCESS"
        } catch {
            Write-Status "Erreur lors de l'ouverture: $($_.Exception.Message)" "ERROR"
        }
    } else {
        Write-Status "Fichier TesterBoutonEnvoi.html non trouvé" "ERROR"
    }
}

# Instructions de test
Write-Host ""
Write-Host "🧪 INSTRUCTIONS DE TEST:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. 📄 TEST AVEC LA PAGE INTERACTIVE:" -ForegroundColor Yellow
Write-Host "   • Ouvrez TesterBoutonEnvoi.html dans votre navigateur" -ForegroundColor White
Write-Host "   • Tapez du texte dans la zone de saisie" -ForegroundColor White
Write-Host "   • Observez le bouton changer de couleur (gris → vert)" -ForegroundColor White
Write-Host "   • Cliquez 'Envoyer' ou appuyez sur Entrée" -ForegroundColor White
Write-Host "   • Vérifiez les logs d'événements" -ForegroundColor White
Write-Host ""

Write-Host "2. 🌐 TEST DANS L'APPLICATION RÉELLE:" -ForegroundColor Yellow
Write-Host "   • Lancez Visual Studio et appuyez sur F5" -ForegroundColor White
Write-Host "   • Naviguez vers /messagerie.aspx" -ForegroundColor White
Write-Host "   • Ouvrez F12 → Console pour voir les logs" -ForegroundColor White
Write-Host "   • Tapez du texte et vérifiez que le bouton s'active" -ForegroundColor White
Write-Host ""

Write-Host "3. 🔍 POINTS DE VÉRIFICATION:" -ForegroundColor Yellow
Write-Host "   ✅ Bouton gris et désactivé par défaut" -ForegroundColor Green
Write-Host "   ✅ Bouton vert et activé quand on tape du texte" -ForegroundColor Green
Write-Host "   ✅ Compteur de caractères mis à jour (0/5000 → X/5000)" -ForegroundColor Green
Write-Host "   ✅ Bouton orange 'Envoi...' pendant l'envoi" -ForegroundColor Green
Write-Host "   ✅ Bouton vert 'Envoyé' après succès" -ForegroundColor Green
Write-Host "   ✅ Message affiché dans la zone de chat" -ForegroundColor Green
Write-Host "   ✅ Champ de saisie vidé après envoi" -ForegroundColor Green
Write-Host ""

Write-Host "4. 🐛 EN CAS DE PROBLÈME:" -ForegroundColor Yellow
Write-Host "   • Ouvrez F12 → Console et cherchez les erreurs JavaScript" -ForegroundColor White
Write-Host "   • Vérifiez que les messages d'initialisation apparaissent:" -ForegroundColor White
Write-Host "     - '🔧 Initialisation des contrôles ASP.NET...'" -ForegroundColor Gray
Write-Host "     - '📝 Message Input: [object HTMLTextAreaElement]'" -ForegroundColor Gray
Write-Host "     - '📤 Send Button: [object HTMLButtonElement]'" -ForegroundColor Gray
Write-Host "   • Si les éléments sont null, vérifiez les IDs ASP.NET" -ForegroundColor White
Write-Host ""

Write-Host "5. 🔧 DÉPANNAGE RAPIDE:" -ForegroundColor Yellow
Write-Host "   • Si le bouton ne s'active pas, tapez dans la console F12:" -ForegroundColor White
Write-Host "     console.log(document.getElementById('ctl00_ContentPlaceHolder1_txtMessage'));" -ForegroundColor Gray
Write-Host "     console.log(document.getElementById('ctl00_ContentPlaceHolder1_btnenvoie'));" -ForegroundColor Gray
Write-Host "   • Les deux doivent retourner des éléments HTML, pas null" -ForegroundColor White
Write-Host ""

# Résumé des corrections
Write-Host "📋 RÉSUMÉ DES CORRECTIONS APPLIQUÉES:" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔧 PROBLÈMES RÉSOLUS:" -ForegroundColor Yellow
Write-Host "   ✅ Bouton reste gris → Maintenant s'active en vert" -ForegroundColor Green
Write-Host "   ✅ Pas de feedback visuel → États visuels complets" -ForegroundColor Green
Write-Host "   ✅ Messages ne s'affichent pas → Affichage immédiat" -ForegroundColor Green
Write-Host "   ✅ Événements JavaScript → Connexion correcte aux contrôles ASP.NET" -ForegroundColor Green
Write-Host ""

Write-Host "💻 CORRECTIONS TECHNIQUES:" -ForegroundColor Yellow
Write-Host "   ✅ Configuration window.aspNetConfig pour les IDs dynamiques" -ForegroundColor Green
Write-Host "   ✅ Fonction initializeAspNetControls() pour la connexion" -ForegroundColor Green
Write-Host "   ✅ Fonction updateSendButtonState() pour les états visuels" -ForegroundColor Green
Write-Host "   ✅ Styles CSS .btn-send.ready, .sending, .sent" -ForegroundColor Green
Write-Host "   ✅ Fonction displayMessageOptimistic() pour l'affichage" -ForegroundColor Green
Write-Host ""

Write-Host "🎨 AMÉLIORATIONS VISUELLES:" -ForegroundColor Yellow
Write-Host "   ✅ Bouton gris (#ccc) quand désactivé" -ForegroundColor Green
Write-Host "   ✅ Bouton vert (gradient #008374) quand prêt" -ForegroundColor Green
Write-Host "   ✅ Bouton orange (#f39c12) pendant l'envoi" -ForegroundColor Green
Write-Host "   ✅ Bouton vert (#27ae60) quand envoyé" -ForegroundColor Green
Write-Host "   ✅ Animation pulse pour le succès" -ForegroundColor Green
Write-Host ""

Write-Host "🧪 OUTILS DE TEST:" -ForegroundColor Yellow
Write-Host "   ✅ TesterBoutonEnvoi.html - Page de test interactive" -ForegroundColor Green
Write-Host "   ✅ Logs JavaScript détaillés dans la console" -ForegroundColor Green
Write-Host "   ✅ Tests automatiques intégrés" -ForegroundColor Green
Write-Host "   ✅ Documentation complète (RESOLUTION_BOUTON_ENVOI.md)" -ForegroundColor Green
Write-Host ""

if ($correctionsOK -eq $corrections.Count) {
    Write-Host "🎉 TOUTES LES CORRECTIONS SONT EN PLACE !" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Votre bouton d'envoi devrait maintenant fonctionner parfaitement :" -ForegroundColor White
    Write-Host "• 🎨 Changement de couleur quand vous tapez" -ForegroundColor Green
    Write-Host "• 📤 Envoi des messages avec feedback visuel" -ForegroundColor Green
    Write-Host "• 💬 Affichage immédiat des messages envoyés" -ForegroundColor Green
    Write-Host "• ⌨️ Raccourcis clavier fonctionnels" -ForegroundColor Green
    Write-Host ""
    Write-Host "La messagerie LinCom est maintenant pleinement opérationnelle ! 🚀" -ForegroundColor Cyan
} else {
    Write-Host "⚠️  CERTAINES CORRECTIONS SEMBLENT MANQUANTES" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Vérifiez que tous les fichiers ont été correctement modifiés." -ForegroundColor White
    Write-Host "Consultez RESOLUTION_BOUTON_ENVOI.md pour plus de détails." -ForegroundColor White
}

Write-Host ""
Write-Host "Appuyez sur une touche pour fermer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
