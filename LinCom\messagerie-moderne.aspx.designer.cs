//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace LinCom
{
    public partial class messagerie_moderne
    {
        /// <summary>
        /// Contrôle hdnConversationId.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hdnConversationId;

        /// <summary>
        /// Contrôle hdnIsGroup.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hdnIsGroup;

        /// <summary>
        /// Contrôle hdnCurrentUserId.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hdnCurrentUserId;

        /// <summary>
        /// Contrôle hdnSelectedContactId.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hdnSelectedContactId;

        /// <summary>
        /// Contrôle lvConversations.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.ListView lvConversations;

        /// <summary>
        /// Contrôle lvMessages.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.ListView lvMessages;

        /// <summary>
        /// Contrôle txtMessage.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlTextArea txtMessage;

        /// <summary>
        /// Contrôle btnEnvoyer.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlButton btnEnvoyer;

        /// <summary>
        /// Contrôle lvContacts.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.ListView lvContacts;
    }
}
