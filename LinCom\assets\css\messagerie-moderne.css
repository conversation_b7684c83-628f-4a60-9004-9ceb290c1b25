/* ===== MESSAGERIE MODERNE - STYLES CSS ===== */

/* Variables CSS */
:root {
    --primary-color: #00d4aa;
    --primary-dark: #00b894;
    --secondary-color: #667eea;
    --accent-color: #764ba2;
    --success-color: #00b894;
    --warning-color: #fdcb6e;
    --error-color: #e17055;
    --info-color: #74b9ff;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-dark: #2d3436;
    --bg-darker: #1e2124;
    
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-muted: #b2bec3;
    --text-white: #ffffff;
    
    --border-color: #ddd;
    --border-light: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    
    --border-radius: 12px;
    --border-radius-small: 8px;
    --border-radius-large: 20px;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
}

/* Reset et Base */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

/* Container Principal */
.modern-chat-container {
    display: flex;
    height: 100vh;
    max-height: 800px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    margin: 20px auto;
    max-width: 1400px;
}

/* ===== SIDEBAR - LISTE DES CONVERSATIONS ===== */
.chat-sidebar {
    width: 350px;
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Sidebar */
.sidebar-header {
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-white);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    position: relative;
    width: 45px;
    height: 45px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--bg-primary);
}

.status-indicator.online { background: var(--success-color); }
.status-indicator.away { background: var(--warning-color); }
.status-indicator.busy { background: var(--error-color); }
.status-indicator.offline { background: var(--text-muted); }

.user-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0;
}

.user-status {
    font-size: var(--font-size-xs);
    opacity: 0.9;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Barre de Recherche */
.search-container {
    padding: 15px 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    z-index: 1;
}

.search-input-wrapper input {
    width: 100%;
    padding: 12px 40px 12px 40px;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-large);
    background: var(--bg-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.search-input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.clear-search {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.clear-search:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Liste des Conversations */
.conversations-list {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.conversations-list::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
    background: transparent;
}

.conversations-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.conversation-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid var(--border-light);
    position: relative;
}

.conversation-item:hover {
    background: var(--bg-secondary);
}

.conversation-item.active {
    background: linear-gradient(90deg, rgba(0, 212, 170, 0.1), rgba(102, 126, 234, 0.1));
    border-right: 3px solid var(--primary-color);
}

.conversation-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    margin-right: 15px;
}

.conversation-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.conversation-content {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.conversation-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    white-space: nowrap;
}

.conversation-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.conversation-badges {
    display: flex;
    align-items: center;
    gap: 8px;
}

.unread-count {
    background: var(--primary-color);
    color: var(--text-white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
}

.unread-count.hidden {
    display: none;
}

.message-status {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-status.sent { color: var(--text-muted); }
.message-status.delivered { color: var(--info-color); }
.message-status.read { color: var(--primary-color); }

/* Empty State */
.empty-conversations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
    color: var(--text-muted);
}

.empty-conversations i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-conversations h3 {
    font-size: var(--font-size-lg);
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.empty-conversations p {
    font-size: var(--font-size-sm);
    max-width: 250px;
}

/* ===== ZONE DE CHAT PRINCIPALE ===== */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    position: relative;
}

/* Header du Chat */
.chat-header {
    padding: 20px 25px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-light);
}

.chat-contact-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.contact-avatar {
    position: relative;
    width: 45px;
    height: 45px;
}

.contact-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.contact-details h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.contact-status {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.chat-actions .btn-icon {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.chat-actions .btn-icon:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

/* Zone des Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.messages-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Messages */
.message {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    max-width: 70%;
    animation: messageSlideIn 0.3s ease-out;
}

.message.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message.received {
    align-self: flex-start;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
}

.message.sent .message-bubble {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-white);
    border-bottom-right-radius: 4px;
}

.message.received .message-bubble {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
    border-bottom-left-radius: 4px;
    box-shadow: var(--shadow-light);
}

.message-text {
    font-size: var(--font-size-base);
    line-height: 1.4;
    margin: 0;
}

.message-attachment {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.message.received .message-attachment {
    border-top-color: var(--border-light);
}

.message-attachment a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.message-attachment a:hover {
    opacity: 1;
    text-decoration: underline;
}

.message-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message.sent .message-info {
    justify-content: flex-end;
}

.message-time {
    white-space: nowrap;
}

/* Indicateur de frappe */
.typing-indicator {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    max-width: 70%;
    align-self: flex-start;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.typing-avatar {
    width: 32px;
    height: 32px;
}

.typing-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.typing-bubble {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    border-bottom-left-radius: 4px;
    padding: 12px 16px;
    box-shadow: var(--shadow-light);
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Empty Chat */
.empty-chat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--text-muted);
}

.empty-chat i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

.empty-chat h3 {
    font-size: var(--font-size-xl);
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.empty-chat p {
    font-size: var(--font-size-base);
    max-width: 300px;
}

/* ===== ZONE DE SAISIE ===== */
.chat-input-container {
    padding: 20px 25px;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-light);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-large);
    padding: 8px;
    border: 1px solid var(--border-light);
    transition: var(--transition-fast);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.btn-attachment {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.btn-attachment:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

.text-input-container {
    flex: 1;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    position: relative;
}

.text-input-container textarea {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.4;
    padding: 8px 0;
    max-height: 120px;
    min-height: 24px;
    color: var(--text-primary);
    outline: none;
}

.text-input-container textarea::placeholder {
    color: var(--text-muted);
}

.btn-emoji {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.btn-emoji:hover {
    background: var(--bg-tertiary);
    color: var(--warning-color);
}

.btn-send {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: var(--text-white);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.btn-send:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.btn-send:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

.input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    padding: 0 8px;
}

.char-counter {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.char-counter.warning {
    color: var(--warning-color);
}

.char-counter.danger {
    color: var(--error-color);
}

/* ===== PANEL LATÉRAL DROIT ===== */
.chat-info-panel {
    width: 300px;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
}

.info-header {
    padding: 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.info-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.btn-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.btn-close:hover {
    background: var(--bg-tertiary);
    color: var(--error-color);
}

.info-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* ===== MODALS ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.search-contacts input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    margin-bottom: 20px;
}

.search-contacts input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.contacts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-fast);
}

.contact-item:hover {
    background: var(--bg-secondary);
}

.contact-avatar {
    width: 40px;
    height: 40px;
}

.contact-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.contact-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.contact-info p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .modern-chat-container {
        margin: 10px;
        height: calc(100vh - 20px);
    }

    .chat-sidebar {
        width: 320px;
    }
}

@media (max-width: 992px) {
    .modern-chat-container {
        margin: 0;
        height: 100vh;
        border-radius: 0;
    }

    .chat-sidebar {
        width: 280px;
    }

    .chat-info-panel {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .modern-chat-container {
        flex-direction: column;
    }

    .chat-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--border-light);
    }

    .conversations-list {
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .conversation-item {
        min-width: 200px;
        border-bottom: none;
        border-right: 1px solid var(--border-light);
    }

    .chat-main {
        height: calc(100vh - 200px);
    }

    .chat-info-panel {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        z-index: 100;
        box-shadow: var(--shadow-heavy);
    }

    .message {
        max-width: 85%;
    }
}

@media (max-width: 480px) {
    .sidebar-header {
        padding: 15px;
    }

    .user-avatar {
        width: 35px;
        height: 35px;
    }

    .user-info h4 {
        font-size: var(--font-size-sm);
    }

    .search-container {
        padding: 10px 15px;
    }

    .conversation-item {
        padding: 12px 15px;
        min-width: 180px;
    }

    .conversation-avatar {
        width: 40px;
        height: 40px;
    }

    .chat-header {
        padding: 15px 20px;
    }

    .contact-avatar {
        width: 35px;
        height: 35px;
    }

    .contact-details h3 {
        font-size: var(--font-size-base);
    }

    .chat-messages {
        padding: 15px;
    }

    .message {
        max-width: 90%;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
    }

    .chat-input-container {
        padding: 15px 20px;
    }

    .input-wrapper {
        padding: 6px;
    }

    .btn-attachment,
    .btn-send {
        width: 36px;
        height: 36px;
    }
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

/* ===== THÈME SOMBRE (OPTIONNEL) ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #2d3436;
        --bg-secondary: #1e2124;
        --bg-tertiary: #36393f;
        --bg-dark: #ffffff;
        --bg-darker: #f8f9fa;

        --text-primary: #ffffff;
        --text-secondary: #b2bec3;
        --text-muted: #636e72;
        --text-white: #2d3436;

        --border-color: #36393f;
        --border-light: #36393f;
    }

    .message.received .message-bubble {
        background: var(--bg-tertiary);
        border-color: #36393f;
    }

    .search-input-wrapper input,
    .search-contacts input {
        background: var(--bg-tertiary);
        border-color: #36393f;
        color: var(--text-primary);
    }

    .input-wrapper {
        background: var(--bg-tertiary);
        border-color: #36393f;
    }
}

/* ===== UTILITAIRES ===== */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-weight-bold {
    font-weight: 600;
}

.font-weight-normal {
    font-weight: 400;
}

.opacity-50 {
    opacity: 0.5;
}

.opacity-75 {
    opacity: 0.75;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

/* ===== SCROLLBAR PERSONNALISÉE ===== */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ===== FOCUS STATES ===== */
.btn-icon:focus,
.btn-attachment:focus,
.btn-emoji:focus,
.btn-send:focus,
.btn-close:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

input:focus,
textarea:focus {
    outline: none;
}

/* ===== PRINT STYLES ===== */
@media print {
    .modern-chat-container {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }

    .sidebar-header,
    .chat-header,
    .chat-input-container,
    .chat-actions,
    .sidebar-actions {
        display: none;
    }

    .chat-sidebar {
        width: 30%;
    }

    .chat-main {
        width: 70%;
    }

    .message {
        break-inside: avoid;
    }
}
