﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{FD03DBB5-988A-488C-871B-8A7B78AEA9A3}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LinCom</RootNamespace>
    <AssemblyName>LinCom</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44319</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AspNet.ScriptManager.bootstrap, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AspNet.ScriptManager.bootstrap.5.2.3\lib\net45\AspNet.ScriptManager.bootstrap.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.jQuery, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AspNet.ScriptManager.jQuery.3.7.1\lib\net45\AspNet.ScriptManager.jQuery.dll</HintPath>
    </Reference>
    <Reference Include="BCrypt.Net-Next, Version=*******, Culture=neutral, PublicKeyToken=1e11be04b6288443, processorArchitecture=MSIL">
      <HintPath>..\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\lib\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.1\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.MSAjax">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.WebForms">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\lib\net45\Microsoft.ScriptManager.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Web.Optimization.WebForms">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\lib\net45\Microsoft.AspNet.Web.Optimization.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls">
      <HintPath>..\packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Web.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.9\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Webpages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Deployment">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Webpages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Webpages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Helpers">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.1\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.9\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.9\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.9\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.9\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="About.aspx" />
    <Content Include="assets\css\main.css" />
    <Content Include="assets\css\messagerie-amelioree.css" />
    <Content Include="assets\img\about-2.jpg" />
    <Content Include="assets\img\about.jpg" />
    <Content Include="assets\img\apple-touch-icon.png" />
    <Content Include="assets\img\blog\blog-1.jpg" />
    <Content Include="assets\img\blog\blog-2.jpg" />
    <Content Include="assets\img\blog\blog-3.jpg" />
    <Content Include="assets\img\blog\blog-4.jpg" />
    <Content Include="assets\img\blog\blog-5.jpg" />
    <Content Include="assets\img\blog\blog-6.jpg" />
    <Content Include="assets\img\blog\blog-author-2.jpg" />
    <Content Include="assets\img\blog\blog-author-3.jpg" />
    <Content Include="assets\img\blog\blog-author-4.jpg" />
    <Content Include="assets\img\blog\blog-author-5.jpg" />
    <Content Include="assets\img\blog\blog-author-6.jpg" />
    <Content Include="assets\img\blog\blog-author.jpg" />
    <Content Include="assets\img\blog\blog-inside-post.jpg" />
    <Content Include="assets\img\blog\blog-recent-1.jpg" />
    <Content Include="assets\img\blog\blog-recent-2.jpg" />
    <Content Include="assets\img\blog\blog-recent-3.jpg" />
    <Content Include="assets\img\blog\blog-recent-4.jpg" />
    <Content Include="assets\img\blog\blog-recent-5.jpg" />
    <Content Include="assets\img\blog\comments-1.jpg" />
    <Content Include="assets\img\blog\comments-2.jpg" />
    <Content Include="assets\img\blog\comments-3.jpg" />
    <Content Include="assets\img\blog\comments-4.jpg" />
    <Content Include="assets\img\blog\comments-5.jpg" />
    <Content Include="assets\img\blog\comments-6.jpg" />
    <Content Include="assets\img\blog\skills.png" />
    <Content Include="assets\img\clients\client-1.png" />
    <Content Include="assets\img\clients\client-2.png" />
    <Content Include="assets\img\clients\client-3.png" />
    <Content Include="assets\img\clients\client-4.png" />
    <Content Include="assets\img\clients\client-5.png" />
    <Content Include="assets\img\clients\client-6.png" />
    <Content Include="assets\img\clients\client-7.png" />
    <Content Include="assets\img\clients\client-8.png" />
    <Content Include="assets\img\clients\skills.png" />
    <Content Include="assets\img\cta-bg.jpg" />
    <Content Include="assets\img\favicon.png" />
    <Content Include="assets\img\forum\skills.png" />
    <Content Include="assets\img\hero-img.svg" />
    <Content Include="assets\img\logo.png" />
    <Content Include="assets\img\mentorat\skills.png" />
    <Content Include="assets\img\portfolio\app-1.jpg" />
    <Content Include="assets\img\portfolio\app-2.jpg" />
    <Content Include="assets\img\portfolio\app-3.jpg" />
    <Content Include="assets\img\portfolio\books-1.jpg" />
    <Content Include="assets\img\portfolio\books-2.jpg" />
    <Content Include="assets\img\portfolio\books-3.jpg" />
    <Content Include="assets\img\portfolio\branding-1.jpg" />
    <Content Include="assets\img\portfolio\branding-2.jpg" />
    <Content Include="assets\img\portfolio\branding-3.jpg" />
    <Content Include="assets\img\portfolio\product-1.jpg" />
    <Content Include="assets\img\portfolio\product-2.jpg" />
    <Content Include="assets\img\portfolio\product-3.jpg" />
    <Content Include="assets\img\portfolio\skills.png" />
    <Content Include="assets\img\services.jpg" />
    <Content Include="assets\img\skills.png" />
    <Content Include="assets\img\stats-img.svg" />
    <Content Include="assets\img\team\team-1.jpg" />
    <Content Include="assets\img\team\team-2.jpg" />
    <Content Include="assets\img\team\team-3.jpg" />
    <Content Include="assets\img\team\team-4.jpg" />
    <Content Include="assets\img\testimonials\skills.png" />
    <Content Include="assets\img\testimonials\testimonials-1.jpg" />
    <Content Include="assets\img\testimonials\testimonials-2.jpg" />
    <Content Include="assets\img\testimonials\testimonials-3.jpg" />
    <Content Include="assets\img\testimonials\testimonials-4.jpg" />
    <Content Include="assets\img\testimonials\testimonials-5.jpg" />
    <Content Include="assets\js\main.js" />
    <Content Include="assets\js\messagerie-amelioree.js" />
    <Content Include="assets\scss\Readme.txt" />
    <Content Include="assets\vendor\aos\aos.cjs.js" />
    <Content Include="assets\vendor\aos\aos.css" />
    <Content Include="assets\vendor\aos\aos.esm.js" />
    <Content Include="assets\vendor\aos\aos.js" />
    <Content Include="assets\vendor\bootstrap-icons\bootstrap-icons.css" />
    <Content Include="assets\vendor\bootstrap-icons\bootstrap-icons.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.rtl.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.rtl.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.rtl.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.rtl.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.rtl.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.rtl.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.min.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.rtl.css" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.rtl.min.css" />
    <Content Include="assets\vendor\bootstrap\css\prb.txt" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.bundle.js" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.bundle.min.js" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.esm.js" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.esm.min.js" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.js" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.min.js" />
    <Content Include="assets\vendor\glightbox\css\glightbox.css" />
    <Content Include="assets\vendor\glightbox\css\glightbox.min.css" />
    <Content Include="assets\vendor\glightbox\js\glightbox.js" />
    <Content Include="assets\vendor\glightbox\js\glightbox.min.js" />
    <Content Include="assets\vendor\imagesloaded\imagesloaded.pkgd.min.js" />
    <Content Include="assets\vendor\isotope-layout\isotope.pkgd.js" />
    <Content Include="assets\vendor\isotope-layout\isotope.pkgd.min.js" />
    <Content Include="assets\vendor\php-email-form\validate.js" />
    <Content Include="assets\vendor\purecounter\purecounter_vanilla.js" />
    <Content Include="assets\vendor\swiper\swiper-bundle.min.css" />
    <Content Include="assets\vendor\swiper\swiper-bundle.min.js" />
    <Content Include="asset\css\style.css" />
    <Content Include="asset\img\apple-touch-icon.png" />
    <Content Include="asset\img\bg.png" />
    <Content Include="asset\img\clients\client-1.png" />
    <Content Include="asset\img\clients\client-2.png" />
    <Content Include="asset\img\clients\client-3.png" />
    <Content Include="asset\img\clients\client-4.png" />
    <Content Include="asset\img\clients\client-5.png" />
    <Content Include="asset\img\clients\client-6.png" />
    <Content Include="asset\img\cta-bg.jpg" />
    <Content Include="asset\img\favicon.png" />
    <Content Include="asset\img\footer-bg.png" />
    <Content Include="asset\img\hero-bg.png" />
    <Content Include="asset\img\hero-header.gif" />
    <Content Include="asset\img\hero-header.png" />
    <Content Include="asset\img\hero-img.png" />
    <Content Include="asset\img\logo3.png" />
    <Content Include="asset\img\portfolio\portfolio-1.jpg" />
    <Content Include="asset\img\portfolio\portfolio-2.jpg" />
    <Content Include="asset\img\portfolio\portfolio-3.jpg" />
    <Content Include="asset\img\portfolio\portfolio-4.jpg" />
    <Content Include="asset\img\portfolio\portfolio-5.jpg" />
    <Content Include="asset\img\portfolio\portfolio-6.jpg" />
    <Content Include="asset\img\portfolio\portfolio-7.jpg" />
    <Content Include="asset\img\portfolio\portfolio-8.jpg" />
    <Content Include="asset\img\portfolio\portfolio-9.jpg" />
    <Content Include="asset\img\portfolio\portfolio-details-1.jpg" />
    <Content Include="asset\img\portfolio\portfolio-details-2.jpg" />
    <Content Include="asset\img\portfolio\portfolio-details-3.jpg" />
    <Content Include="asset\img\skills.png" />
    <Content Include="asset\img\team\co1.jfif" />
    <Content Include="asset\img\team\co2.jfif" />
    <Content Include="asset\img\team\co3.jfif" />
    <Content Include="asset\img\team\co4.jfif" />
    <Content Include="asset\img\team\team-1.jpg" />
    <Content Include="asset\img\team\team-2.jpg" />
    <Content Include="asset\img\team\team-3.jpg" />
    <Content Include="asset\img\team\team-4.jpg" />
    <Content Include="asset\img\why-us.png" />
    <Content Include="asset\js\main.js" />
    <Content Include="asset\vendor\aos\aos.css" />
    <Content Include="asset\vendor\aos\aos.js" />
    <Content Include="asset\vendor\bootstrap-icons\bootstrap-icons.css" />
    <Content Include="asset\vendor\bootstrap-icons\index.html" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.rtl.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.rtl.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.rtl.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.rtl.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.rtl.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.rtl.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.min.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.rtl.css" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.rtl.min.css" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.bundle.js" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.bundle.min.js" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.esm.js" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.esm.min.js" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.js" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.min.js" />
    <Content Include="asset\vendor\boxicons\css\animations.css" />
    <Content Include="asset\vendor\boxicons\css\boxicons.css" />
    <Content Include="asset\vendor\boxicons\css\boxicons.min.css" />
    <Content Include="asset\vendor\boxicons\css\transformations.css" />
    <Content Include="asset\vendor\boxicons\fonts\boxicons.svg" />
    <Content Include="asset\vendor\glightbox\css\glightbox.css" />
    <Content Include="asset\vendor\glightbox\css\glightbox.min.css" />
    <Content Include="asset\vendor\glightbox\js\glightbox.js" />
    <Content Include="asset\vendor\glightbox\js\glightbox.min.js" />
    <Content Include="asset\vendor\isotope-layout\isotope.pkgd.js" />
    <Content Include="asset\vendor\isotope-layout\isotope.pkgd.min.js" />
    <Content Include="asset\vendor\php-email-form\validate.js" />
    <Content Include="asset\vendor\remixicon\remixicon.css" />
    <Content Include="asset\vendor\remixicon\remixicon.svg" />
    <Content Include="asset\vendor\remixicon\remixicon.symbol.svg" />
    <Content Include="asset\vendor\swiper\swiper-bundle.min.css" />
    <Content Include="asset\vendor\swiper\swiper-bundle.min.js" />
    <Content Include="asset\vendor\waypoints\noframework.waypoints.js" />
    <Content Include="bibliotheque.aspx" />
    <Content Include="blog-details.html" />
    <Content Include="blog.html" />
    <Content Include="blogong.aspx" />
    <Content Include="communiquer.aspx" />
    <Content Include="Contact.aspx" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-grid.rtl.css" />
    <Content Include="Content\bootstrap-grid.rtl.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-reboot.rtl.css" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css" />
    <Content Include="Content\bootstrap-utilities.css" />
    <Content Include="Content\bootstrap-utilities.min.css" />
    <Content Include="Content\bootstrap-utilities.rtl.css" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\bootstrap.rtl.css" />
    <Content Include="Content\bootstrap.rtl.min.css" />
    <Content Include="Content\Site.css" />
    <Content Include="d.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="details.aspx" />
    <Content Include="evenement-ong.aspx" />
    <Content Include="events.aspx" />
    <Content Include="espaceformation.aspx" />
    <Content Include="espaceforum.aspx" />
    <Content Include="evenement.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="file\404.html" />
    <Content Include="file\accordion.html" />
    <Content Include="file\activiteprojet.aspx" />
    <Content Include="file\actualit\skills.png" />
    <Content Include="file\alert.html" />
    <Content Include="file\analytics.html" />
    <Content Include="file\animations.html" />
    <Content Include="file\area-charts.html" />
    <Content Include="file\article.aspx" />
    <Content Include="file\bar-charts.html" />
    <Content Include="file\buttons.html" />
    <Content Include="file\categoriepost.aspx" />
    <Content Include="file\chosen-sprite.png" />
    <Content Include="file\code-editor.html" />
    <Content Include="file\color.html" />
    <Content Include="file\commune.aspx" />
    <Content Include="file\compose-email.html" />
    <Content Include="file\conditionutilisation.aspx" />
    <Content Include="file\contact.html" />
    <Content Include="file\css\animate.css" />
    <Content Include="file\css\animation\animation-custom.css" />
    <Content Include="file\css\bootstrap-select\bootstrap-select.css" />
    <Content Include="file\css\bootstrap.min.css" />
    <Content Include="file\css\c3\c3.min.css" />
    <Content Include="file\css\chosen\chosen-sprite.png" />
    <Content Include="file\css\chosen\chosen.css" />
    <Content Include="file\css\code-editor\1.jpg" />
    <Content Include="file\css\code-editor\ambiance.css" />
    <Content Include="file\css\code-editor\codemirror.css" />
    <Content Include="file\css\color-picker\farbtastic.css" />
    <Content Include="file\css\color-picker\marker.png" />
    <Content Include="file\css\color-picker\mask.png" />
    <Content Include="file\css\color-picker\wheel.png" />
    <Content Include="file\css\cropper\cropper.min.css" />
    <Content Include="file\css\datapicker\datepicker3.css" />
    <Content Include="file\css\dialog\dialog.css" />
    <Content Include="file\css\dialog\sweetalert2.min.css" />
    <Content Include="file\css\dropzone\dropzone.css" />
    <Content Include="file\css\font-awesome.min.css" />
    <Content Include="file\css\jquery.dataTables.min.css" />
    <Content Include="file\css\jvectormap\jquery-jvectormap-2.0.3.css" />
    <Content Include="file\css\main.css" />
    <Content Include="file\css\meanmenu\meanmenu.min.css" />
    <Content Include="file\css\metisMenu\metisMenu-vertical.css" />
    <Content Include="file\css\metisMenu\metisMenu.min.css" />
    <Content Include="file\css\normalize.css" />
    <Content Include="file\css\notification\notification.css" />
    <Content Include="file\css\notika-custom-icon.css" />
    <Content Include="file\css\owl.carousel.css" />
    <Content Include="file\css\owl.theme.css" />
    <Content Include="file\css\owl.transitions.css" />
    <Content Include="file\css\responsive.css" />
    <Content Include="file\css\scrollbar\jquery.mCustomScrollbar.min.css" />
    <Content Include="file\css\summernote\summernote.css" />
    <Content Include="file\css\themesaller-forms.css" />
    <Content Include="file\css\wave\button.css" />
    <Content Include="file\css\wave\waves.min.css" />
    <Content Include="file\dashboard.aspx" />
    <Content Include="file\data-map.html" />
    <Content Include="file\data-table.html" />
    <Content Include="file\dialog.html" />
    <Content Include="file\domaineintervention.aspx" />
    <Content Include="file\domaineinterventionorganisation.aspx" />
    <Content Include="file\domainepost.aspx" />
    <Content Include="file\domaineressources.aspx" />
    <Content Include="file\dropdown.html" />
    <Content Include="file\envoiemail.aspx" />
    <Content Include="file\faq.aspx" />
    <Content Include="file\financement.aspx" />
    <Content Include="file\flot-charts.html" />
    <Content Include="file\fonts\fontawesome-webfont.svg" />
    <Content Include="file\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="file\fonts\notika-icon.svg" />
    <Content Include="file\form-components.html" />
    <Content Include="file\form-elements.html" />
    <Content Include="file\form-examples.html" />
    <Content Include="file\formations.aspx" />
    <Content Include="file\google-map.html" />
    <Content Include="file\image-cropper.html" />
    <Content Include="file\images\select.png" />
    <Content Include="file\images\sort_asc.png" />
    <Content Include="file\images\sort_asc_disabled.png" />
    <Content Include="file\images\sort_both.png" />
    <Content Include="file\images\sort_desc.png" />
    <Content Include="file\images\sort_desc_disabled.png" />
    <Content Include="file\img\blog\1.jpg" />
    <Content Include="file\img\chosen-sprite.png" />
    <Content Include="file\img\country\1.png" />
    <Content Include="file\img\country\2.png" />
    <Content Include="file\img\country\3.png" />
    <Content Include="file\img\cropper\1.jpg" />
    <Content Include="file\img\dialog\like.png" />
    <Content Include="file\img\favicon.ico" />
    <Content Include="file\img\green1.png" />
    <Content Include="file\img\logo\favicon.png" />
    <Content Include="file\img\logo\logo.png" />
    <Content Include="file\img\post\1.jpg" />
    <Content Include="file\img\post\2.jpg" />
    <Content Include="file\img\post\4.jpg" />
    <Content Include="file\img\search-engines\baidu.png" />
    <Content Include="file\img\search-engines\bing.png" />
    <Content Include="file\img\search-engines\duckduckgo.png" />
    <Content Include="file\img\search-engines\google.png" />
    <Content Include="file\img\search-engines\yahoo.png" />
    <Content Include="file\img\search-engines\yandex.png" />
    <Content Include="file\img\widgets\2.png" />
    <Content Include="file\img\widgets\4.png" />
    <Content Include="file\img\widgets\6.png" />
    <Content Include="file\inbox.html" />
    <Content Include="file\index-2.html" />
    <Content Include="file\index-3.html" />
    <Content Include="file\index-4.html" />
    <Content Include="file\index.html" />
    <Content Include="file\invoice.html" />
    <Content Include="file\js\animation\animation-active.js" />
    <Content Include="file\js\autosize.min.js" />
    <Content Include="file\js\bootstrap-select\bootstrap-select.js" />
    <Content Include="file\js\bootstrap.min.js" />
    <Content Include="file\js\charts\area-chart.js" />
    <Content Include="file\js\charts\bar-chart.js" />
    <Content Include="file\js\charts\Chart.js" />
    <Content Include="file\js\charts\line-chart.js" />
    <Content Include="file\js\chat\jquery.chat.js" />
    <Content Include="file\js\chat\moment.min.js" />
    <Content Include="file\js\chosen\chosen.jquery.js" />
    <Content Include="file\js\code-editor\code-editor-active.js" />
    <Content Include="file\js\code-editor\code-editor.js" />
    <Content Include="file\js\code-editor\codemirror.js" />
    <Content Include="file\js\color-picker\color-picker.js" />
    <Content Include="file\js\color-picker\farbtastic.min.js" />
    <Content Include="file\js\counterup\counterup-active.js" />
    <Content Include="file\js\counterup\jquery.counterup.min.js" />
    <Content Include="file\js\counterup\waypoints.min.js" />
    <Content Include="file\js\cropper\cropper-actice.js" />
    <Content Include="file\js\cropper\cropper.min.js" />
    <Content Include="file\js\data-map\d3.min.js" />
    <Content Include="file\js\data-map\data-maps-active.js" />
    <Content Include="file\js\data-map\datamaps.all.min.js" />
    <Content Include="file\js\data-map\topojson.js" />
    <Content Include="file\js\data-table\data-table-act.js" />
    <Content Include="file\js\data-table\jquery.dataTables.min.js" />
    <Content Include="file\js\datapicker\bootstrap-datepicker.js" />
    <Content Include="file\js\datapicker\datepicker-active.js" />
    <Content Include="file\js\dialog\dialog-active.js" />
    <Content Include="file\js\dialog\sweetalert2.min.js" />
    <Content Include="file\js\dropzone\dropzone.js" />
    <Content Include="file\js\easytopie\jquery.easypiechart.min.js" />
    <Content Include="file\js\easytopie\salvattore.min.js" />
    <Content Include="file\js\flot\analtic-flot-active.js" />
    <Content Include="file\js\flot\chart-tooltips.js" />
    <Content Include="file\js\flot\curvedLines.js" />
    <Content Include="file\js\flot\flot-active.js" />
    <Content Include="file\js\flot\flot-widget-anatic-active.js" />
    <Content Include="file\js\flot\jquery.flot.js" />
    <Content Include="file\js\flot\jquery.flot.orderBars.js" />
    <Content Include="file\js\flot\jquery.flot.pie.js" />
    <Content Include="file\js\flot\jquery.flot.resize.js" />
    <Content Include="file\js\flot\jquery.flot.time.js" />
    <Content Include="file\js\flot\jquery.flot.tooltip.min.js" />
    <Content Include="file\js\google.maps\google.maps-active.js" />
    <Content Include="file\js\google.maps\google.maps2-active.js" />
    <Content Include="file\js\icheck\icheck-active.js" />
    <Content Include="file\js\icheck\icheck.min.js" />
    <Content Include="file\js\jasny-bootstrap.min.js" />
    <Content Include="file\js\jquery-price-slider.js" />
    <Content Include="file\js\jquery.scrollUp.min.js" />
    <Content Include="file\js\jvectormap\jquery-jvectormap-2.0.2.min.js" />
    <Content Include="file\js\jvectormap\jquery-jvectormap-world-mill-en.js" />
    <Content Include="file\js\jvectormap\jvectormap-active.js" />
    <Content Include="file\js\knob\jquery.appear.js" />
    <Content Include="file\js\knob\jquery.knob.js" />
    <Content Include="file\js\knob\knob-active.js" />
    <Content Include="file\js\login\login-action.js" />
    <Content Include="file\js\main.js" />
    <Content Include="file\js\meanmenu\jquery.meanmenu.js" />
    <Content Include="file\js\metisMenu\metisMenu-active.js" />
    <Content Include="file\js\metisMenu\metisMenu.min.js" />
    <Content Include="file\js\notification\bootstrap-growl.min.js" />
    <Content Include="file\js\notification\notification-active.js" />
    <Content Include="file\js\owl.carousel.min.js" />
    <Content Include="file\js\plugins.js" />
    <Content Include="file\js\rangle-slider\jquery-ui-1.10.4.custom.min.js" />
    <Content Include="file\js\rangle-slider\jquery-ui-touch-punch.min.js" />
    <Content Include="file\js\rangle-slider\rangle-active.js" />
    <Content Include="file\js\scrollbar\jquery.mCustomScrollbar.concat.min.js" />
    <Content Include="file\js\sparkline\jquery.sparkline.min.js" />
    <Content Include="file\js\sparkline\sparkline-active.js" />
    <Content Include="file\js\summernote\summernote-active.js" />
    <Content Include="file\js\summernote\summernote-updated.min.js" />
    <Content Include="file\js\summernote\summernote.min.js" />
    <Content Include="file\js\tawk-chat.js" />
    <Content Include="file\js\todo\jquery.todo.js" />
    <Content Include="file\js\vendor\jquery-1.12.4.min.js" />
    <Content Include="file\js\vendor\modernizr-2.8.3.min.js" />
    <Content Include="file\js\wave\wave-active.js" />
    <Content Include="file\js\wave\waves.min.js" />
    <Content Include="file\js\wizard\jquery.bootstrap.wizard.min.js" />
    <Content Include="file\js\wizard\wizard-active.js" />
    <Content Include="file\js\wow.min.js" />
    <Content Include="file\line-charts.html" />
    <Content Include="file\listactiviteprojet.aspx" />
    <Content Include="file\listarticle.aspx" />
    <Content Include="file\listcategoriepost.aspx" />
    <Content Include="file\listcommune.aspx" />
    <Content Include="file\listdomaineintervention.aspx" />
    <Content Include="file\listdomaineinterventionorganisation.aspx" />
    <Content Include="file\listdomainepost.aspx" />
    <Content Include="file\listdomaineressources.aspx" />
    <Content Include="file\listfaq.aspx" />
    <Content Include="file\listfinancement.aspx" />
    <Content Include="file\listformations.aspx" />
    <Content Include="file\listmembre.aspx" />
    <Content Include="file\listmentor.aspx" />
    <Content Include="file\listmentore.aspx" />
    <Content Include="file\listmentorprogramme.aspx" />
    <Content Include="file\listorganisation.aspx" />
    <Content Include="file\listpartenaire.aspx" />
    <Content Include="file\listposts.aspx" />
    <Content Include="file\listprogramme.aspx" />
    <Content Include="file\listprogrammementorat.aspx" />
    <Content Include="file\listprojet.aspx" />
    <Content Include="file\listprovince.aspx" />
    <Content Include="file\listressources.aspx" />
    <Content Include="file\listroles.aspx" />
    <Content Include="file\listservice.aspx" />
    <Content Include="file\listsessionmentorat.aspx" />
    <Content Include="file\listtypeorganisation.aspx" />
    <Content Include="file\login-register.html" />
    <Content Include="file\membre.aspx" />
    <Content Include="file\membr\emptyuser.png" />
    <Content Include="file\mentor.aspx" />
    <Content Include="file\mentore.aspx" />
    <Content Include="file\mentorprogramme.aspx" />
    <Content Include="file\modals.html" />
    <Content Include="file\normal-table.html" />
    <Content Include="file\notification.html" />
    <Content Include="file\organisation.aspx" />
    <Content Include="file\organ\orga.png" />
    <Content Include="file\organ\4.png" />
    <Content Include="file\organ\energy.jpeg" />
    <Content Include="file\partenaire.aspx" />
    <Content Include="file\partenaireorganisation.aspx" />
    <Content Include="file\politiqueconfidentialite.aspx" />
    <Content Include="file\popovers.html" />
    <Content Include="file\posts.aspx" />
    <Content Include="file\post\skills.png" />
    <Content Include="file\programme.aspx" />
    <Content Include="file\programmementorat.aspx" />
    <Content Include="file\projet.aspx" />
    <Content Include="file\province.aspx" />
    <Content Include="file\ressources.aspx" />
    <Content Include="file\role.aspx" />
    <Content Include="file\service.aspx" />
    <Content Include="file\sessionmentorat.aspx" />
    <Content Include="file\style.css" />
    <Content Include="file\tabs.html" />
    <Content Include="file\tooltips.html" />
    <Content Include="file\typeorganisation.aspx" />
    <Content Include="file\typography.html" />
    <Content Include="file\view-email.html" />
    <Content Include="file\widgets.html" />
    <Content Include="file\wizard.html" />
    <Content Include="forgot.aspx" />
    <Content Include="formation.aspx" />
    <Content Include="forms\Readme.txt" />
    <Content Include="forums.aspx" />
    <Content Include="galeriemedia.aspx" />
    <Content Include="Global.asax" />
    <Content Include=".DS_Store" />
    <Content Include="assets\.DS_Store" />
    <Content Include="assets\vendor\aos\aos.js.map" />
    <Content Include="assets\vendor\bootstrap-icons\bootstrap-icons.json" />
    <Content Include="assets\vendor\bootstrap-icons\bootstrap-icons.scss" />
    <Content Include="assets\vendor\bootstrap-icons\fonts\bootstrap-icons.woff" />
    <Content Include="assets\vendor\bootstrap-icons\fonts\bootstrap-icons.woff2" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.rtl.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-grid.rtl.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.rtl.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.rtl.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.min.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.rtl.css.map" />
    <Content Include="assets\vendor\bootstrap\css\bootstrap.rtl.min.css.map" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.bundle.js.map" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.bundle.min.js.map" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.esm.js.map" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.esm.min.js.map" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.js.map" />
    <Content Include="assets\vendor\bootstrap\js\bootstrap.min.js.map" />
    <Content Include="assets\vendor\purecounter\purecounter_vanilla.js.map" />
    <Content Include="assets\vendor\swiper\swiper-bundle.min.js.map" />
    <Content Include="forms\contact.php" />
    <Content Include="home.aspx" />
    <Content Include="inscriptionmembre.aspx" />
    <Content Include="inscriptionorganisation.aspx" />
    <Content Include="login.aspx" />
    <Content Include="mailing.aspx" />
    <Content Include="mentorat.aspx" />
    <Content Include="messagerie.aspx" />
    <Content Include="test-messagerie.aspx" />
    <Content Include="Model\ModelLincom.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ModelLincom.edmx</DependentUpon>
      <LastGenOutput>ModelLincom.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Model\ModelLincom.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ModelLincom.edmx</DependentUpon>
      <LastGenOutput>ModelLincom.cs</LastGenOutput>
    </Content>
    <Content Include="notification.aspx" />
    <Content Include="ong.aspx" />
    <Content Include="ongorg.aspx" />
    <Content Include="org.aspx" />
    <Content Include="PageMaster.Master" />
    <Content Include="index.html" />
    <Content Include="portfolio-details.html" />
    <Content Include="program.aspx" />
    <Content Include="programme.aspx" />
    <Content Include="programmementorat.aspx" />
    <Content Include="Readme.txt" />
    <Content Include="README_VS2022.md" />
    <Content Include="AMELIORATIONS_MESSAGERIE.md" />
    <Content Include="GUIDE_VISUAL_STUDIO_2022.md" />
    <Content Include="VerifierIntegration.ps1" />
    <Content Include="LancerProjet.ps1" />
    <Content Include="INTEGRATION_COMPLETE.md" />
    <Content Include="GUIDE_RESOLUTION_ERREURS.md" />
    <Content Include="EXEMPLE_UTILISATION.md" />
    <Content Include="GUIDE_ERREUR_LINQ.md" />
    <Content Include="CORRECTIONS_APPLIQUEES.md" />
    <Content Include="SUPPRESSION_MESSAGERIE_MODERNE.md" />
    <Content Include="AMELIORATIONS_INTERFACE_MESSAGERIE.md" />
    <Content Include="reset.aspx" />
    <Content Include="ressource.aspx" />
    <Content Include="ressources-document.aspx" />
    <Content Include="ressources-formation.aspx" />
    <Content Include="ressources-rapport.aspx" />
    <Content Include="ressources.aspx" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.esm.js" />
    <Content Include="Scripts\bootstrap.esm.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.esm.min.js.map" />
    <Content Include="Scripts\bootstrap.esm.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\bootstrap.rtl.min.css.map" />
    <Content Include="Content\bootstrap.rtl.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.css.map" />
    <Content Include="Content\bootstrap-utilities.min.css.map" />
    <Content Include="Content\bootstrap-utilities.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.min.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <Content Include="file\fonts\fontawesome-webfont.eot" />
    <Content Include="file\fonts\fontawesome-webfont.ttf" />
    <Content Include="file\fonts\fontawesome-webfont.woff" />
    <Content Include="file\fonts\fontawesome-webfont.woff2" />
    <Content Include="file\fonts\FontAwesome.otf" />
    <Content Include="file\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="file\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="file\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="file\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="file\fonts\notika-icon.eot" />
    <Content Include="file\fonts\notika-icon.ttf" />
    <Content Include="file\fonts\notika-icon.woff" />
    <Content Include="file\OfficeMaster.Master" />
    <Content Include="asset\vendor\bootstrap-icons\bootstrap-icons.json" />
    <Content Include="asset\vendor\bootstrap-icons\fonts\bootstrap-icons.woff" />
    <Content Include="asset\vendor\bootstrap-icons\fonts\bootstrap-icons.woff2" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.rtl.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-grid.rtl.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.rtl.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.rtl.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.min.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.rtl.css.map" />
    <Content Include="asset\vendor\bootstrap\css\bootstrap.rtl.min.css.map" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.bundle.js.map" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.bundle.min.js.map" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.esm.js.map" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.esm.min.js.map" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.js.map" />
    <Content Include="asset\vendor\bootstrap\js\bootstrap.min.js.map" />
    <Content Include="asset\vendor\boxicons\fonts\boxicons.eot" />
    <Content Include="asset\vendor\boxicons\fonts\boxicons.ttf" />
    <Content Include="asset\vendor\boxicons\fonts\boxicons.woff" />
    <Content Include="asset\vendor\boxicons\fonts\boxicons.woff2" />
    <Content Include="asset\vendor\remixicon\remixicon.eot" />
    <Content Include="asset\vendor\remixicon\remixicon.less" />
    <Content Include="asset\vendor\remixicon\remixicon.ttf" />
    <Content Include="asset\vendor\remixicon\remixicon.woff" />
    <Content Include="asset\vendor\remixicon\remixicon.woff2" />
    <Content Include="assets\pdf\Lincom.pdf" />
    <EntityDeploy Include="Model\ModelLincom.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>ModelLincom.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Model\ModelLincom.edmx.diagram">
      <DependentUpon>ModelLincom.edmx</DependentUpon>
    </Content>
    <None Include="Scripts\jquery-3.7.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.7.1.js" />
    <Content Include="Scripts\jquery-3.7.1.min.js" />
    <Content Include="Scripts\jquery-3.7.1.slim.js" />
    <Content Include="Scripts\jquery-3.7.1.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="service-details.html" />
    <Content Include="Site.Master" />
    <Content Include="starter-page.html" />
    <Content Include="video.aspx" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="Web.config" />
    <Content Include="Bundle.config" />
    <Content Include="Site.Mobile.Master" />
    <Content Include="Views\web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="About.aspx.cs">
      <DependentUpon>About.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="About.aspx.designer.cs">
      <DependentUpon>About.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="bibliotheque.aspx.cs">
      <DependentUpon>bibliotheque.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="bibliotheque.aspx.designer.cs">
      <DependentUpon>bibliotheque.aspx</DependentUpon>
    </Compile>
    <Compile Include="blogong.aspx.cs">
      <DependentUpon>blogong.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="blogong.aspx.designer.cs">
      <DependentUpon>blogong.aspx</DependentUpon>
    </Compile>
    <Compile Include="Classe\ActiviteProjet_Class.cs" />
    <Compile Include="Classe\AvisRessource_Class.cs" />
    <Compile Include="Classe\BudgetsActivite_Class.cs" />
    <Compile Include="Classe\CategoriePost_Class.cs" />
    <Compile Include="Classe\CommentairePoste_Class.cs" />
    <Compile Include="Classe\CommuneClass.cs" />
    <Compile Include="Classe\ConditionUtilisation_Class.cs" />
    <Compile Include="Classe\Conversation_Class.cs" />
    <Compile Include="Classe\DomaineFormation_Class.cs" />
    <Compile Include="Classe\DomaineInterventionOrganisation_Class.cs" />
    <Compile Include="Classe\DomaineIntervention_Class.cs" />
    <Compile Include="Classe\DomainePost_Class.cs" />
    <Compile Include="Classe\DomaineRessource_Class.cs" />
    <Compile Include="Classe\DomaineRessourc_Class.cs" />
    <Compile Include="Classe\FAQ_Class.cs" />
    <Compile Include="Classe\Feedback_Class.cs" />
    <Compile Include="Classe\FichierMessage_Class.cs" />
    <Compile Include="Classe\Financement_Class.cs" />
    <Compile Include="Classe\Formation_Class.cs" />
    <Compile Include="Classe\Forum_Class.cs" />
    <Compile Include="Classe\Langue_Class.cs" />
    <Compile Include="Classe\MembreProfil_Class.cs" />
    <Compile Include="Classe\MembresOrganisation_Class.cs" />
    <Compile Include="Classe\Membre_Class.cs" />
    <Compile Include="Classe\Mentoree_Class.cs" />
    <Compile Include="Classe\Mentore_Class.cs" />
    <Compile Include="Classe\Mentor_Class.cs" />
    <Compile Include="Classe\MenuPermission_Class.cs" />
    <Compile Include="Classe\Menu_Class.cs" />
    <Compile Include="Classe\MessageStatus_Class.cs" />
    <Compile Include="Classe\Message_Class.cs" />
    <Compile Include="Classe\Notification_Class.cs" />
    <Compile Include="Classe\Organisation_Class.cs" />
    <Compile Include="Classe\ParametreApplication_Class.cs" />
    <Compile Include="Classe\PartenaireOrganisation_Class.cs" />
    <Compile Include="Classe\Partenaire_Class.cs" />
    <Compile Include="Classe\ParticipantConversation_Class.cs" />
    <Compile Include="Classe\Permission_Class.cs" />
    <Compile Include="Classe\PolitiqueConfidentialite_Class.cs" />
    <Compile Include="Classe\Post_Class.cs" />
    <Compile Include="Classe\PreferenceCooky_Class.cs" />
    <Compile Include="Classe\ProgrammeMentorat_Class.cs" />
    <Compile Include="Classe\ProgrammesEtInitiative_Class.cs" />
    <Compile Include="Classe\ProjetPartenaire_Class.cs" />
    <Compile Include="Classe\Projet_Class.cs" />
    <Compile Include="Classe\Province_Class.cs" />
    <Compile Include="Classe\ReplyForum_Class.cs" />
    <Compile Include="Classe\Ressources_Class.cs" />
    <Compile Include="Classe\RoleMembre_Class.cs" />
    <Compile Include="Classe\SessionMentorat_Class.cs" />
    <Compile Include="Classe\StatutUtilisateur_Class.cs" />
    <Compile Include="Classe\Subscription_Class.cs" />
    <Compile Include="Classe\SujetForum_Class.cs" />
    <Compile Include="Classe\SupportFormation_Class.cs" />
    <Compile Include="Classe\TelechargementRessources_Class.cs" />
    <Compile Include="Classe\Traduction_Class.cs" />
    <Compile Include="Classe\TypeOrganisation_Class.cs" />
    <Compile Include="communiquer.aspx.cs">
      <DependentUpon>communiquer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="communiquer.aspx.designer.cs">
      <DependentUpon>communiquer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Contact.aspx.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Contact.aspx.designer.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="d.aspx.cs">
      <DependentUpon>d.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="d.aspx.designer.cs">
      <DependentUpon>d.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="details.aspx.cs">
      <DependentUpon>details.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="details.aspx.designer.cs">
      <DependentUpon>details.aspx</DependentUpon>
    </Compile>
    <Compile Include="evenement-ong.aspx.cs">
      <DependentUpon>evenement-ong.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="evenement-ong.aspx.designer.cs">
      <DependentUpon>evenement-ong.aspx</DependentUpon>
    </Compile>
    <Compile Include="events.aspx.cs">
      <DependentUpon>events.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="events.aspx.designer.cs">
      <DependentUpon>events.aspx</DependentUpon>
    </Compile>
    <Compile Include="espaceformation.aspx.cs">
      <DependentUpon>espaceformation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="espaceformation.aspx.designer.cs">
      <DependentUpon>espaceformation.aspx</DependentUpon>
    </Compile>
    <Compile Include="espaceforum.aspx.cs">
      <DependentUpon>espaceforum.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="espaceforum.aspx.designer.cs">
      <DependentUpon>espaceforum.aspx</DependentUpon>
    </Compile>
    <Compile Include="evenement.aspx.cs">
      <DependentUpon>evenement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="evenement.aspx.designer.cs">
      <DependentUpon>evenement.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\activiteprojet.aspx.cs">
      <DependentUpon>activiteprojet.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\activiteprojet.aspx.designer.cs">
      <DependentUpon>activiteprojet.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\article.aspx.cs">
      <DependentUpon>article.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\article.aspx.designer.cs">
      <DependentUpon>article.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\categoriepost.aspx.cs">
      <DependentUpon>categoriepost.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\categoriepost.aspx.designer.cs">
      <DependentUpon>categoriepost.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\commune.aspx.cs">
      <DependentUpon>commune.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\commune.aspx.designer.cs">
      <DependentUpon>commune.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\conditionutilisation.aspx.cs">
      <DependentUpon>conditionutilisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\conditionutilisation.aspx.designer.cs">
      <DependentUpon>conditionutilisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\dashboard.aspx.cs">
      <DependentUpon>dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\dashboard.aspx.designer.cs">
      <DependentUpon>dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\domaineintervention.aspx.cs">
      <DependentUpon>domaineintervention.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\domaineintervention.aspx.designer.cs">
      <DependentUpon>domaineintervention.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\domaineinterventionorganisation.aspx.cs">
      <DependentUpon>domaineinterventionorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\domaineinterventionorganisation.aspx.designer.cs">
      <DependentUpon>domaineinterventionorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\domainepost.aspx.cs">
      <DependentUpon>domainepost.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\domainepost.aspx.designer.cs">
      <DependentUpon>domainepost.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\domaineressources.aspx.cs">
      <DependentUpon>domaineressources.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\domaineressources.aspx.designer.cs">
      <DependentUpon>domaineressources.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\envoiemail.aspx.cs">
      <DependentUpon>envoiemail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\envoiemail.aspx.designer.cs">
      <DependentUpon>envoiemail.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\faq.aspx.cs">
      <DependentUpon>faq.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\faq.aspx.designer.cs">
      <DependentUpon>faq.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\financement.aspx.cs">
      <DependentUpon>financement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\financement.aspx.designer.cs">
      <DependentUpon>financement.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\formations.aspx.cs">
      <DependentUpon>formations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\formations.aspx.designer.cs">
      <DependentUpon>formations.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listactiviteprojet.aspx.cs">
      <DependentUpon>listactiviteprojet.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listactiviteprojet.aspx.designer.cs">
      <DependentUpon>listactiviteprojet.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listarticle.aspx.cs">
      <DependentUpon>listarticle.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listarticle.aspx.designer.cs">
      <DependentUpon>listarticle.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listcategoriepost.aspx.cs">
      <DependentUpon>listcategoriepost.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listcategoriepost.aspx.designer.cs">
      <DependentUpon>listcategoriepost.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listcommune.aspx.cs">
      <DependentUpon>listcommune.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listcommune.aspx.designer.cs">
      <DependentUpon>listcommune.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listdomaineintervention.aspx.cs">
      <DependentUpon>listdomaineintervention.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listdomaineintervention.aspx.designer.cs">
      <DependentUpon>listdomaineintervention.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listdomaineinterventionorganisation.aspx.cs">
      <DependentUpon>listdomaineinterventionorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listdomaineinterventionorganisation.aspx.designer.cs">
      <DependentUpon>listdomaineinterventionorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listdomainepost.aspx.cs">
      <DependentUpon>listdomainepost.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listdomainepost.aspx.designer.cs">
      <DependentUpon>listdomainepost.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listdomaineressources.aspx.cs">
      <DependentUpon>listdomaineressources.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listdomaineressources.aspx.designer.cs">
      <DependentUpon>listdomaineressources.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listfaq.aspx.cs">
      <DependentUpon>listfaq.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listfaq.aspx.designer.cs">
      <DependentUpon>listfaq.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listfinancement.aspx.cs">
      <DependentUpon>listfinancement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listfinancement.aspx.designer.cs">
      <DependentUpon>listfinancement.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listformations.aspx.cs">
      <DependentUpon>listformations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listformations.aspx.designer.cs">
      <DependentUpon>listformations.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listmembre.aspx.cs">
      <DependentUpon>listmembre.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listmembre.aspx.designer.cs">
      <DependentUpon>listmembre.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listmentor.aspx.cs">
      <DependentUpon>listmentor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listmentor.aspx.designer.cs">
      <DependentUpon>listmentor.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listmentore.aspx.cs">
      <DependentUpon>listmentore.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listmentore.aspx.designer.cs">
      <DependentUpon>listmentore.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listmentorprogramme.aspx.cs">
      <DependentUpon>listmentorprogramme.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listmentorprogramme.aspx.designer.cs">
      <DependentUpon>listmentorprogramme.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listorganisation.aspx.cs">
      <DependentUpon>listorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listorganisation.aspx.designer.cs">
      <DependentUpon>listorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listpartenaire.aspx.cs">
      <DependentUpon>listpartenaire.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listpartenaire.aspx.designer.cs">
      <DependentUpon>listpartenaire.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listposts.aspx.cs">
      <DependentUpon>listposts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listposts.aspx.designer.cs">
      <DependentUpon>listposts.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listprogramme.aspx.cs">
      <DependentUpon>listprogramme.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listprogramme.aspx.designer.cs">
      <DependentUpon>listprogramme.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listprogrammementorat.aspx.cs">
      <DependentUpon>listprogrammementorat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listprogrammementorat.aspx.designer.cs">
      <DependentUpon>listprogrammementorat.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listprojet.aspx.cs">
      <DependentUpon>listprojet.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listprojet.aspx.designer.cs">
      <DependentUpon>listprojet.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listprovince.aspx.cs">
      <DependentUpon>listprovince.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listprovince.aspx.designer.cs">
      <DependentUpon>listprovince.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listressources.aspx.cs">
      <DependentUpon>listressources.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listressources.aspx.designer.cs">
      <DependentUpon>listressources.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listroles.aspx.cs">
      <DependentUpon>listroles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listroles.aspx.designer.cs">
      <DependentUpon>listroles.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listservice.aspx.cs">
      <DependentUpon>listservice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listservice.aspx.designer.cs">
      <DependentUpon>listservice.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listsessionmentorat.aspx.cs">
      <DependentUpon>listsessionmentorat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listsessionmentorat.aspx.designer.cs">
      <DependentUpon>listsessionmentorat.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\listtypeorganisation.aspx.cs">
      <DependentUpon>listtypeorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\listtypeorganisation.aspx.designer.cs">
      <DependentUpon>listtypeorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\membre.aspx.cs">
      <DependentUpon>membre.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\membre.aspx.designer.cs">
      <DependentUpon>membre.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\mentor.aspx.cs">
      <DependentUpon>mentor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\mentor.aspx.designer.cs">
      <DependentUpon>mentor.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\mentore.aspx.cs">
      <DependentUpon>mentore.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\mentore.aspx.designer.cs">
      <DependentUpon>mentore.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\mentorprogramme.aspx.cs">
      <DependentUpon>mentorprogramme.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\mentorprogramme.aspx.designer.cs">
      <DependentUpon>mentorprogramme.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\OfficeMaster.Master.cs">
      <DependentUpon>OfficeMaster.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\OfficeMaster.Master.designer.cs">
      <DependentUpon>OfficeMaster.Master</DependentUpon>
    </Compile>
    <Compile Include="file\organisation.aspx.cs">
      <DependentUpon>organisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\organisation.aspx.designer.cs">
      <DependentUpon>organisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\partenaire.aspx.cs">
      <DependentUpon>partenaire.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\partenaire.aspx.designer.cs">
      <DependentUpon>partenaire.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\partenaireorganisation.aspx.cs">
      <DependentUpon>partenaireorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\partenaireorganisation.aspx.designer.cs">
      <DependentUpon>partenaireorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\politiqueconfidentialite.aspx.cs">
      <DependentUpon>politiqueconfidentialite.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\politiqueconfidentialite.aspx.designer.cs">
      <DependentUpon>politiqueconfidentialite.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\posts.aspx.cs">
      <DependentUpon>posts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\posts.aspx.designer.cs">
      <DependentUpon>posts.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\programme.aspx.cs">
      <DependentUpon>programme.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\programme.aspx.designer.cs">
      <DependentUpon>programme.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\programmementorat.aspx.cs">
      <DependentUpon>programmementorat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\programmementorat.aspx.designer.cs">
      <DependentUpon>programmementorat.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\projet.aspx.cs">
      <DependentUpon>projet.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\projet.aspx.designer.cs">
      <DependentUpon>projet.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\province.aspx.cs">
      <DependentUpon>province.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\province.aspx.designer.cs">
      <DependentUpon>province.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\ressources.aspx.cs">
      <DependentUpon>ressources.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\ressources.aspx.designer.cs">
      <DependentUpon>ressources.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\role.aspx.cs">
      <DependentUpon>role.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\role.aspx.designer.cs">
      <DependentUpon>role.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\service.aspx.cs">
      <DependentUpon>service.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\service.aspx.designer.cs">
      <DependentUpon>service.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\sessionmentorat.aspx.cs">
      <DependentUpon>sessionmentorat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\sessionmentorat.aspx.designer.cs">
      <DependentUpon>sessionmentorat.aspx</DependentUpon>
    </Compile>
    <Compile Include="file\typeorganisation.aspx.cs">
      <DependentUpon>typeorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="file\typeorganisation.aspx.designer.cs">
      <DependentUpon>typeorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="forgot.aspx.cs">
      <DependentUpon>forgot.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="forgot.aspx.designer.cs">
      <DependentUpon>forgot.aspx</DependentUpon>
    </Compile>
    <Compile Include="formation.aspx.cs">
      <DependentUpon>formation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="formation.aspx.designer.cs">
      <DependentUpon>formation.aspx</DependentUpon>
    </Compile>
    <Compile Include="forums.aspx.cs">
      <DependentUpon>forums.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="forums.aspx.designer.cs">
      <DependentUpon>forums.aspx</DependentUpon>
    </Compile>
    <Compile Include="galeriemedia.aspx.cs">
      <DependentUpon>galeriemedia.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="galeriemedia.aspx.designer.cs">
      <DependentUpon>galeriemedia.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="home.aspx.cs">
      <DependentUpon>home.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="home.aspx.designer.cs">
      <DependentUpon>home.aspx</DependentUpon>
    </Compile>
    <Compile Include="Imp\ActiviteProjetImp.cs" />
    <Compile Include="Imp\APIYoutube_Class.cs" />
    <Compile Include="Imp\AvisRessourceImp.cs" />
    <Compile Include="Imp\BudgetsActiviteImp.cs" />
    <Compile Include="Imp\CategoriePostImp.cs" />
    <Compile Include="Imp\CommentaireImp.cs" />
    <Compile Include="Imp\CommonCode.cs" />
    <Compile Include="Imp\CommuneImp.cs" />
    <Compile Include="Imp\ConditionUtilisationImp.cs" />
    <Compile Include="Imp\ConversationImp.cs" />
    <Compile Include="Imp\DomaineInterventionImp.cs" />
    <Compile Include="Imp\DomaineInterventionOrganisationImp.cs" />
    <Compile Include="Imp\DomainePostImp.cs" />
    <Compile Include="Imp\DomaineRessourceImp.cs" />
    <Compile Include="Imp\FAQImp.cs" />
    <Compile Include="Imp\FinancementImp.cs" />
    <Compile Include="Imp\FormationImp.cs" />
    <Compile Include="Imp\ForumImp.cs" />
    <Compile Include="Imp\IActiviteProjet.cs" />
    <Compile Include="Imp\IAvisRessource.cs" />
    <Compile Include="Imp\IBudgetsActivite.cs" />
    <Compile Include="Imp\ICategoryPost.cs" />
    <Compile Include="Imp\ICommentairePoste.cs" />
    <Compile Include="Imp\ICommonCode.cs" />
    <Compile Include="Imp\ICommune.cs" />
    <Compile Include="Imp\IConditionUtilisation.cs" />
    <Compile Include="Imp\IConversation.cs" />
    <Compile Include="Imp\IDomaineIntervention.cs" />
    <Compile Include="Imp\IDomaineInterventionOrganisation.cs" />
    <Compile Include="Imp\IDomainePost.cs" />
    <Compile Include="Imp\IDomaineRessource.cs" />
    <Compile Include="Imp\IFAQ.cs" />
    <Compile Include="Imp\IFinancement.cs" />
    <Compile Include="Imp\IFormation.cs" />
    <Compile Include="Imp\IForum.cs" />
    <Compile Include="Imp\ILangue.cs" />
    <Compile Include="Imp\IMembre.cs" />
    <Compile Include="Imp\IMembreProfil.cs" />
    <Compile Include="Imp\IMembresOrganisation.cs" />
    <Compile Include="Imp\IMentor.cs" />
    <Compile Include="Imp\IMentore.cs" />
    <Compile Include="Imp\IMenu.cs" />
    <Compile Include="Imp\IMenuPermission.cs" />
    <Compile Include="Imp\IMessage.cs" />
    <Compile Include="Imp\INotification.cs" />
    <Compile Include="Imp\IOrganisation.cs" />
    <Compile Include="Imp\IParametreApplication.cs" />
    <Compile Include="Imp\IPartenaire.cs" />
    <Compile Include="Imp\IPartenaireorganisation.cs" />
    <Compile Include="Imp\IPermission.cs" />
    <Compile Include="Imp\IPolitiqueConfidentialite.cs" />
    <Compile Include="Imp\IPorgrammeInitiative.cs" />
    <Compile Include="Imp\IPoste.cs" />
    <Compile Include="Imp\IProgrammeMentorat.cs" />
    <Compile Include="Imp\IProjet.cs" />
    <Compile Include="Imp\IProvince.cs" />
    <Compile Include="Imp\IReplyForum.cs" />
    <Compile Include="Imp\IRessource.cs" />
    <Compile Include="Imp\IRoleMembre.cs" />
    <Compile Include="Imp\ISessionMentorat.cs" />
    <Compile Include="Imp\IStatutUtilisateur.cs" />
    <Compile Include="Imp\ISubscription.cs" />
    <Compile Include="Imp\ISujetForm.cs" />
    <Compile Include="Imp\ISujetForum.cs" />
    <Compile Include="Imp\ISupportFormation.cs" />
    <Compile Include="Imp\ITelechargement.cs" />
    <Compile Include="Imp\ITypeOrganisation.cs" />
    <Compile Include="Imp\LangueImp.cs" />
    <Compile Include="Imp\MembreImp.cs" />
    <Compile Include="Imp\MembreProfilImp.cs" />
    <Compile Include="Imp\MembresOrganisationImp.cs" />
    <Compile Include="Imp\MentoreImp.cs" />
    <Compile Include="Imp\MentorImp.cs" />
    <Compile Include="Imp\MenuImp.cs" />
    <Compile Include="Imp\MenuPermissionImp.cs" />
    <Compile Include="Imp\MessageImp.cs" />
    <Compile Include="Imp\SecurityHelper.cs" />
    <Compile Include="Imp\CacheHelper.cs" />
    <Compile Include="Imp\DataControlHelper.cs" />
    <Compile Include="Imp\NotificationImp.cs" />
    <Compile Include="Imp\OrganisationImp.cs" />
    <Compile Include="Imp\ParametreApplicationImp.cs" />
    <Compile Include="Imp\PartenaireImp.cs" />
    <Compile Include="Imp\PartenaireOrganisationImp.cs" />
    <Compile Include="Imp\PermissionImp.cs" />
    <Compile Include="Imp\PolitiqueConfidentialiteImp.cs" />
    <Compile Include="Imp\PosteImp.cs" />
    <Compile Include="Imp\ProgrammeInitiativeImp.cs" />
    <Compile Include="Imp\ProgrammeMentoratImp.cs" />
    <Compile Include="Imp\ProjetImp.cs" />
    <Compile Include="Imp\ProvinceImp.cs" />
    <Compile Include="Imp\ReplyForumImp.cs" />
    <Compile Include="Imp\RessourceImp.cs" />
    <Compile Include="Imp\RoleMembreImp.cs" />
    <Compile Include="Imp\SessionMentoratImp.cs" />
    <Compile Include="Imp\StatutUtilisateurImp.cs" />
    <Compile Include="Imp\SubscriptionImp.cs" />
    <Compile Include="Imp\SujetForumImp.cs" />
    <Compile Include="Imp\SupportFormationImp.cs" />
    <Compile Include="Imp\TelechargementImp.cs" />
    <Compile Include="Imp\TypeOrganisationImp.cs" />
    <Compile Include="inscriptionmembre.aspx.cs">
      <DependentUpon>inscriptionmembre.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="inscriptionmembre.aspx.designer.cs">
      <DependentUpon>inscriptionmembre.aspx</DependentUpon>
    </Compile>
    <Compile Include="inscriptionorganisation.aspx.cs">
      <DependentUpon>inscriptionorganisation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="inscriptionorganisation.aspx.designer.cs">
      <DependentUpon>inscriptionorganisation.aspx</DependentUpon>
    </Compile>
    <Compile Include="login.aspx.cs">
      <DependentUpon>login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="login.aspx.designer.cs">
      <DependentUpon>login.aspx</DependentUpon>
    </Compile>
    <Compile Include="mailing.aspx.cs">
      <DependentUpon>mailing.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="mailing.aspx.designer.cs">
      <DependentUpon>mailing.aspx</DependentUpon>
    </Compile>
    <Compile Include="mentorat.aspx.cs">
      <DependentUpon>mentorat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="mentorat.aspx.designer.cs">
      <DependentUpon>mentorat.aspx</DependentUpon>
    </Compile>
    <Compile Include="messagerie.aspx.cs">
      <DependentUpon>messagerie.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="messagerie.aspx.designer.cs">
      <DependentUpon>messagerie.aspx</DependentUpon>
    </Compile>
    <Compile Include="test-messagerie.aspx.cs">
      <DependentUpon>test-messagerie.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="test-messagerie.aspx.designer.cs">
      <DependentUpon>test-messagerie.aspx</DependentUpon>
    </Compile>
    <Compile Include="Model\ActiviteProjet.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\AvisRessource.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\budgets_activite.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\CategoriePost.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\CommentPost.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Commune.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ConditionUtilisation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Conversation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ConversationParticipant.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\DomaineFormation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\DomaineIntervention.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\DomaineInterventionOrganisation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\DomainePost.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\DomaineProjet.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\DomaineRessource.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\FAQ.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\FeedbackMentor.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\FeedbackMentoree.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\FichierMessage.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Financement.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Formation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Forum.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Langue.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Membre.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\MembreProfil.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\MembresOrganisation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Mentor.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Mentore.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\menu.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\menupermission.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Message.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\MessageStatu.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ModelLincom.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModelLincom.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ModelLincom.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ModelLincom.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModelLincom.edmx</DependentUpon>
    </Compile>
    <Compile Include="Model\Notification.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Organisation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ParamettreApplication.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Partenaire.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\PartenairesOrganisation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ParticipantConversation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\permission.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\PolitiqueConfidentialite.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Post.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\PreferenceCooky.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Programmementorat.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\ProgrammesEtInitiative.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Projet.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Province.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\RepliesForum.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Ressource.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\rolemembre.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\SessionMentorat.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\StatutUtilisateur.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Subscription.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\SujetForum.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\SupportFormation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\TelechargementRessource.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\Traduction.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\TypeOrganisation.cs">
      <DependentUpon>ModelLincom.tt</DependentUpon>
    </Compile>
    <Compile Include="notification.aspx.cs">
      <DependentUpon>notification.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="notification.aspx.designer.cs">
      <DependentUpon>notification.aspx</DependentUpon>
    </Compile>
    <Compile Include="ong.aspx.cs">
      <DependentUpon>ong.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ong.aspx.designer.cs">
      <DependentUpon>ong.aspx</DependentUpon>
    </Compile>
    <Compile Include="ongorg.aspx.cs">
      <DependentUpon>ongorg.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ongorg.aspx.designer.cs">
      <DependentUpon>ongorg.aspx</DependentUpon>
    </Compile>
    <Compile Include="org.aspx.cs">
      <DependentUpon>org.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="org.aspx.designer.cs">
      <DependentUpon>org.aspx</DependentUpon>
    </Compile>
    <Compile Include="PageMaster.Master.cs">
      <DependentUpon>PageMaster.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PageMaster.Master.designer.cs">
      <DependentUpon>PageMaster.Master</DependentUpon>
    </Compile>
    <Compile Include="program.aspx.cs">
      <DependentUpon>program.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="program.aspx.designer.cs">
      <DependentUpon>program.aspx</DependentUpon>
    </Compile>
    <Compile Include="programme.aspx.cs">
      <DependentUpon>programme.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="programme.aspx.designer.cs">
      <DependentUpon>programme.aspx</DependentUpon>
    </Compile>
    <Compile Include="programmementorat.aspx.cs">
      <DependentUpon>programmementorat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="programmementorat.aspx.designer.cs">
      <DependentUpon>programmementorat.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="reset.aspx.cs">
      <DependentUpon>reset.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="reset.aspx.designer.cs">
      <DependentUpon>reset.aspx</DependentUpon>
    </Compile>
    <Compile Include="ressource.aspx.cs">
      <DependentUpon>ressource.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ressource.aspx.designer.cs">
      <DependentUpon>ressource.aspx</DependentUpon>
    </Compile>
    <Compile Include="ressources-document.aspx.cs">
      <DependentUpon>ressources-document.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ressources-document.aspx.designer.cs">
      <DependentUpon>ressources-document.aspx</DependentUpon>
    </Compile>
    <Compile Include="ressources-formation.aspx.cs">
      <DependentUpon>ressources-formation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ressources-formation.aspx.designer.cs">
      <DependentUpon>ressources-formation.aspx</DependentUpon>
    </Compile>
    <Compile Include="ressources-rapport.aspx.cs">
      <DependentUpon>ressources-rapport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ressources-rapport.aspx.designer.cs">
      <DependentUpon>ressources-rapport.aspx</DependentUpon>
    </Compile>
    <Compile Include="ressources.aspx.cs">
      <DependentUpon>ressources.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ressources.aspx.designer.cs">
      <DependentUpon>ressources.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="video.aspx.cs">
      <DependentUpon>video.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="video.aspx.designer.cs">
      <DependentUpon>video.aspx</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Controller\" />
    <Folder Include="file\activeproje\" />
    <Folder Include="file\partnerorg\" />
    <Folder Include="file\partner\" />
    <Folder Include="file\programentorat\" />
    <Folder Include="file\program\" />
    <Folder Include="file\proje\" />
    <Folder Include="file\ressourc\" />
    <Folder Include="file\servic\" />
    <Folder Include="images\" />
    <Folder Include="Models\" />
    <Folder Include="NewFolder1\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.7.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.7.1.min.map" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>62474</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44319/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>