using System;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Security;

namespace LinCom.Imp
{
    /// <summary>
    /// Classe utilitaire pour la sécurité et la validation
    /// </summary>
    public static class SecurityHelper
    {
        /// <summary>
        /// Génère un token CSRF pour la protection contre les attaques CSRF
        /// </summary>
        /// <returns>Token CSRF</returns>
        public static string GenerateCSRFToken()
        {
            if (HttpContext.Current.Session["CSRFToken"] == null)
            {
                HttpContext.Current.Session["CSRFToken"] = Guid.NewGuid().ToString();
            }
            return HttpContext.Current.Session["CSRFToken"].ToString();
        }

        /// <summary>
        /// Valide un token CSRF
        /// </summary>
        /// <param name="token">Token à valider</param>
        /// <returns>True si le token est valide</returns>
        public static bool ValidateCSRFToken(string token)
        {
            var sessionToken = HttpContext.Current.Session["CSRFToken"]?.ToString();
            return !string.IsNullOrEmpty(sessionToken) && sessionToken == token;
        }

        /// <summary>
        /// Sanitise le contenu HTML pour éviter les attaques XSS
        /// </summary>
        /// <param name="input">Contenu à sanitiser</param>
        /// <returns>Contenu sanitisé</returns>
        public static string SanitizeHtml(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Encoder les caractères HTML
            string sanitized = HttpUtility.HtmlEncode(input);

            // Supprimer les scripts dangereux
            sanitized = Regex.Replace(sanitized, @"<script[^>]*>.*?</script>", "", RegexOptions.IgnoreCase);
            sanitized = Regex.Replace(sanitized, @"javascript:", "", RegexOptions.IgnoreCase);
            sanitized = Regex.Replace(sanitized, @"on\w+\s*=", "", RegexOptions.IgnoreCase);
            sanitized = Regex.Replace(sanitized, @"<iframe[^>]*>.*?</iframe>", "", RegexOptions.IgnoreCase);
            sanitized = Regex.Replace(sanitized, @"<object[^>]*>.*?</object>", "", RegexOptions.IgnoreCase);
            sanitized = Regex.Replace(sanitized, @"<embed[^>]*>.*?</embed>", "", RegexOptions.IgnoreCase);

            return sanitized.Trim();
        }

        /// <summary>
        /// Valide un message avant l'envoi
        /// </summary>
        /// <param name="contenu">Contenu du message</param>
        /// <param name="maxLength">Longueur maximale autorisée</param>
        /// <returns>True si le message est valide</returns>
        public static bool ValidateMessage(string contenu, int maxLength = 5000)
        {
            if (string.IsNullOrWhiteSpace(contenu))
                return false;

            if (contenu.Length > maxLength)
                return false;

            // Vérifier qu'il n'y a pas que des espaces ou caractères spéciaux
            if (Regex.IsMatch(contenu.Trim(), @"^[\s\r\n\t]*$"))
                return false;

            return true;
        }

        /// <summary>
        /// Valide une adresse email
        /// </summary>
        /// <param name="email">Email à valider</param>
        /// <returns>True si l'email est valide</returns>
        public static bool ValidateEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Génère un hash sécurisé pour les mots de passe
        /// </summary>
        /// <param name="password">Mot de passe à hasher</param>
        /// <returns>Hash du mot de passe</returns>
        public static string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        /// <summary>
        /// Vérifie un mot de passe contre son hash
        /// </summary>
        /// <param name="password">Mot de passe en clair</param>
        /// <param name="hash">Hash à vérifier</param>
        /// <returns>True si le mot de passe correspond</returns>
        public static bool VerifyPassword(string password, string hash)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hash);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Valide un ID (doit être positif)
        /// </summary>
        /// <param name="id">ID à valider</param>
        /// <returns>True si l'ID est valide</returns>
        public static bool ValidateId(long id)
        {
            return id > 0;
        }

        /// <summary>
        /// Nettoie une chaîne de recherche
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Terme nettoyé</returns>
        public static string CleanSearchTerm(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return string.Empty;

            // Supprimer les caractères dangereux pour les requêtes SQL
            searchTerm = Regex.Replace(searchTerm, @"[';--/*]", "");
            
            // Limiter la longueur
            if (searchTerm.Length > 100)
                searchTerm = searchTerm.Substring(0, 100);

            return searchTerm.Trim();
        }

        /// <summary>
        /// Vérifie si une chaîne contient des caractères SQL dangereux
        /// </summary>
        /// <param name="input">Chaîne à vérifier</param>
        /// <returns>True si la chaîne est suspecte</returns>
        public static bool ContainsSqlInjection(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            string[] sqlKeywords = { 
                "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", 
                "ALTER", "EXEC", "EXECUTE", "UNION", "SCRIPT", "--", "/*", "*/" 
            };

            string upperInput = input.ToUpper();
            foreach (string keyword in sqlKeywords)
            {
                if (upperInput.Contains(keyword))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Limite le taux de requêtes par utilisateur (simple implémentation)
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <param name="maxRequests">Nombre maximum de requêtes</param>
        /// <param name="timeWindow">Fenêtre de temps en minutes</param>
        /// <returns>True si l'utilisateur peut faire une requête</returns>
        public static bool CheckRateLimit(long userId, int maxRequests = 60, int timeWindow = 1)
        {
            string key = $"RateLimit_{userId}";
            var session = HttpContext.Current.Session;
            
            if (session[key] == null)
            {
                session[key] = new { Count = 1, LastReset = DateTime.Now };
                return true;
            }

            dynamic rateLimitData = session[key];
            DateTime lastReset = rateLimitData.LastReset;
            int count = rateLimitData.Count;

            // Réinitialiser si la fenêtre de temps est dépassée
            if (DateTime.Now.Subtract(lastReset).TotalMinutes >= timeWindow)
            {
                session[key] = new { Count = 1, LastReset = DateTime.Now };
                return true;
            }

            // Vérifier si la limite est atteinte
            if (count >= maxRequests)
                return false;

            // Incrémenter le compteur
            session[key] = new { Count = count + 1, LastReset = lastReset };
            return true;
        }
    }
}
